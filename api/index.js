/**
 * 统一 API 处理器 - Vercel Hobby 计划版本
 * 所有 API 逻辑都在这个文件中，避免创建多个 Serverless Functions
 */

import { createClient } from '@supabase/supabase-js';

// Supabase 配置
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables');
}

// 创建 Supabase 客户端
const supabase = createClient(supabaseUrl, supabaseAnonKey);
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

// CORS 工具函数
function applyCorsHeaders(res) {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
}

function handleCorsPreflightRequest(req, res) {
  if (req.method === 'OPTIONS') {
    applyCorsHeaders(res);
    res.status(200).end();
    return true;
  }
  return false;
}

// 响应工具函数
function sendSuccess(res, data, message = 'Success', statusCode = 200) {
  applyCorsHeaders(res);
  res.status(statusCode).json({
    code: statusCode,
    message,
    data
  });
}

function sendError(res, message = 'Error', statusCode = 500) {
  applyCorsHeaders(res);
  res.status(statusCode).json({
    code: statusCode,
    message,
    data: null
  });
}

function sendNotFound(res, message = 'Not Found') {
  sendError(res, message, 404);
}

// 验证工具函数
function validateDateFormat(dateString) {
  const regex = /^\d{4}-\d{2}-\d{2}$/;
  return regex.test(dateString);
}

function validateLength(str, min, max) {
  return str && str.length >= min && str.length <= max;
}

// API 处理函数

// 健康检查
async function handleHealth(req, res) {
  const memoryUsage = process.memoryUsage();
  
  return sendSuccess(res, {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    version: '1.0.0',
    server: {
      status: 'online',
      uptime: process.uptime(),
      version: '1.0.0'
    },
    memory: {
      rss: Math.round(memoryUsage.rss / 1024 / 1024) + 'MB',
      heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) + 'MB',
      heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024) + 'MB'
    }
  }, 'API is healthy');
}

// 网站配置
async function handleSiteConfig(req, res) {
  const defaultConfig = {
    title: '毛双欢和张家伟的小窝',
    logo: '一二&布布',
    writing: '喜欢花 喜欢浪漫 喜欢你~',
    boy: 'zjw',
    girl: 'msh', 
    boyQQ: '**********',
    girlQQ: '**********',
    startTime: '2023-12-29 00:00:00',
    animation: '1',
    copyright: '',
    icp: '',
  };

  if (req.method === 'GET') {
    try {
      const { data: configData, error } = await supabase
        .from('site_config')
        .select('key, value');
      
      if (error) {
        console.error('获取网站配置失败:', error);
        return sendSuccess(res, defaultConfig, '获取成功');
      }
      
      const config = { ...defaultConfig };
      if (configData) {
        configData.forEach(item => {
          switch (item.key) {
            case 'site_name':
              config.title = item.value;
              break;
            case 'love_start_date':
              config.startTime = item.value + ' 00:00:00';
              break;
            case 'boy_name':
              config.boy = item.value;
              break;
            case 'girl_name':
              config.girl = item.value;
              break;
            case 'boy_qq':
              config.boyQQ = item.value;
              break;
            case 'girl_qq':
              config.girlQQ = item.value;
              break;
            case 'site_description':
              config.writing = item.value;
              break;
            default:
              config[item.key] = item.value;
          }
        });
      }
      
      return sendSuccess(res, config, '获取成功');
    } catch (error) {
      console.error('Error in site config endpoint:', error);
      return sendError(res, '获取网站配置失败', 500);
    }
  }
  
  if (req.method === 'PUT') {
    try {
      const updates = req.body;
      
      if (!updates || Object.keys(updates).length === 0) {
        return sendError(res, '更新数据不能为空', 400);
      }
      
      const updatePromises = Object.entries(updates).map(([key, value]) => {
        return supabase
          .from('site_config')
          .upsert({ key, value }, { onConflict: 'key' });
      });
      
      await Promise.all(updatePromises);
      
      return sendSuccess(res, updates, '更新成功');
    } catch (error) {
      console.error('更新网站配置失败:', error);
      return sendError(res, '更新网站配置失败', 500);
    }
  }
  
  return sendError(res, '不支持的请求方法', 405);
}

// 文章 API
async function handleArticles(req, res, urlPath = '') {
  if (req.method === 'GET') {
    try {
      // 解析动态路由
      const pathSegments = urlPath.split('/').filter(Boolean);
      
      // 处理 /articles/:id/related 路由
      if (pathSegments.length >= 2 && pathSegments[1] === 'related') {
        const articleId = pathSegments[0];
        const { limit = 5 } = req.query;
        
        // 首先获取当前文章的分类
        const { data: currentArticle, error: currentError } = await supabase
          .from('articles')
          .select('category')
          .eq('id', articleId)
          .single();
        
        if (currentError) {
          console.error('获取当前文章失败:', currentError);
          return sendError(res, '获取文章失败', 500);
        }
        
        if (!currentArticle) {
          return sendError(res, '文章不存在', 404);
        }
        
        // 获取相同分类的其他文章
        const { data: relatedArticles, error: relatedError } = await supabase
          .from('articles')
          .select('*')
          .eq('category', currentArticle.category)
          .neq('id', articleId)
          .order('created_at', { ascending: false })
          .limit(parseInt(limit));
        
        if (relatedError) {
          console.error('获取相关文章失败:', relatedError);
          return sendError(res, '获取相关文章失败', 500);
        }
        
        return sendSuccess(res, relatedArticles || [], '获取相关文章成功');
      }
      
      // 处理 /articles/:id 路由
      if (pathSegments.length === 1) {
        const articleId = pathSegments[0];
        
        const { data: article, error } = await supabase
          .from('articles')
          .select('*')
          .eq('id', articleId)
          .single();
        
        if (error) {
          console.error('获取文章详情失败:', error);
          return sendError(res, '获取文章详情失败', 500);
        }
        
        if (!article) {
          return sendError(res, '文章不存在', 404);
        }
        
        return sendSuccess(res, article, '获取文章详情成功');
      }
      
      // 处理文章列表 /articles
      const {
        page = 1,
        limit = 10,
        keyword = '',
        category = '',
        tags = '',
        startDate = '',
        endDate = '',
        author = '',
        sortBy = 'created_at',
        sortOrder = 'desc'
      } = req.query;
      
      const offset = (page - 1) * limit;
      
      let query = supabase.from('articles').select('*', { count: 'exact' });
      
      if (keyword) {
        query = query.or(`title.ilike.%${keyword}%,content.ilike.%${keyword}%,summary.ilike.%${keyword}%`);
      }
      
      if (author) {
        query = query.eq('author', author);
      }
      
      if (tags) {
        const parsedTags = tags.split(',');
        query = query.overlaps('tags', parsedTags);
      }
      
      if (startDate && validateDateFormat(startDate)) {
        query = query.gte('created_at', startDate);
      }
      
      if (endDate && validateDateFormat(endDate)) {
        query = query.lte('created_at', endDate);
      }
      
      const ascending = sortOrder === 'asc';
      query = query.order(sortBy, { ascending });
      query = query.range(offset, offset + limit - 1);
      
      const { data: articles, error, count } = await query;
      
      if (error) {
        console.error('获取文章列表失败:', error);
        return sendError(res, '获取文章列表失败', 500);
      }
      
      return sendSuccess(res, {
        list: articles || [],
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit)
        }
      }, '获取成功');
    } catch (error) {
      console.error('Error in articles endpoint:', error);
      return sendError(res, '获取文章失败', 500);
    }
  }
  
  if (req.method === 'POST') {
    try {
      const { title, content, author, tags, category, summary } = req.body;
      
      if (!title || !content) {
        return sendError(res, '标题和内容不能为空', 400);
      }
      
      const { data: newArticle, error } = await supabase
        .from('articles')
        .insert([{
          title,
          content,
          author: author || '张家伟',
          summary: summary || '',
          tags: tags || [],
          category: category || '未分类'
        }])
        .select()
        .single();
      
      if (error) {
        console.error('创建文章失败:', error);
        return sendError(res, '创建文章失败', 500);
      }
      
      return sendSuccess(res, newArticle, '文章创建成功');
    } catch (error) {
      console.error('创建文章异常:', error);
      return sendError(res, '创建文章失败', 500);
    }
  }
  
  return sendError(res, '不支持的请求方法', 405);
}

// 留言 API
async function handleMessages(req, res) {
  if (req.method === 'GET') {
    try {
      const {
        page = 1,
        limit = 10,
        keyword = '',
        startDate = '',
        endDate = ''
      } = req.query;
      
      const offset = (page - 1) * limit;
      
      let query = supabase.from('messages').select('*', { count: 'exact' });
      
      if (keyword) {
        query = query.or(`name.ilike.%${keyword}%,content.ilike.%${keyword}%`);
      }
      
      if (startDate && validateDateFormat(startDate)) {
        query = query.gte('created_at', startDate);
      }
      
      if (endDate && validateDateFormat(endDate)) {
        query = query.lte('created_at', endDate);
      }
      
      query = query.order('created_at', { ascending: false });
      query = query.range(offset, offset + limit - 1);
      
      const { data: messages, error, count } = await query;
      
      if (error) {
        console.error('获取留言列表失败:', error);
        return sendError(res, '获取留言列表失败', 500);
      }
      
      return sendSuccess(res, {
        list: messages || [],
        total: count || 0,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil((count || 0) / limit)
      }, '获取成功');
    } catch (error) {
      console.error('Error in messages endpoint:', error);
      return sendError(res, '获取留言列表失败', 500);
    }
  }
  
  if (req.method === 'POST') {
    try {
      const { name, email, content } = req.body;
      
      if (!name || !content) {
        return sendError(res, '姓名和留言内容不能为空', 400);
      }
      
      if (!validateLength(content, 1, 500)) {
        return sendError(res, '留言内容长度应在1-500字符之间', 400);
      }
      
      const { data: newMessage, error } = await supabase
        .from('messages')
        .insert([{
          name,
          email: email || null,
          content
        }])
        .select()
        .single();
      
      if (error) {
        console.error('创建留言失败:', error);
        return sendError(res, '留言提交失败', 500);
      }
      
      return sendSuccess(res, newMessage, '留言提交成功', 201);
    } catch (error) {
      console.error('Error in messages endpoint:', error);
      return sendError(res, '留言提交失败', 500);
    }
  }
  
  return sendError(res, '不支持的请求方法', 405);
}

// 恋爱相册 API
async function handleLoveImages(req, res) {
  if (req.method === 'GET') {
    try {
      const {
        page = 1,
        limit = 20,
        keyword = '',
        startDate = '',
        endDate = ''
      } = req.query;

      const offset = (page - 1) * limit;

      let query = supabase.from('love_images').select('*', { count: 'exact' });

      if (keyword) {
        query = query.ilike('description', `%${keyword}%`);
      }

      if (startDate && validateDateFormat(startDate)) {
        query = query.gte('image_date', startDate);
      }

      if (endDate && validateDateFormat(endDate)) {
        query = query.lte('image_date', endDate);
      }

      query = query.order('image_date', { ascending: false });
      query = query.range(offset, offset + limit - 1);

      const { data: images, error, count } = await query;

      if (error) {
        console.error('获取恋爱相册失败:', error);
        return sendError(res, '获取恋爱相册失败', 500);
      }

      // 转换数据格式以匹配前端期望
      const transformedImages = (images || []).map(record => ({
        id: record.id,
        imgUrl: record.image_url,
        imgText: record.description || '',
        imgDatd: record.image_date,
        createTime: record.created_at,
        updateTime: record.updated_at,
        title: record.title || '',
        thumbnailUrl: record.thumbnail_url,
        tags: record.tags || []
      }));

      return sendSuccess(res, {
        list: transformedImages,
        total: count || 0,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil((count || 0) / limit)
      }, '获取成功');
    } catch (error) {
      console.error('Error in love images endpoint:', error);
      return sendError(res, '处理恋爱相册请求失败', 500);
    }
  }

  if (req.method === 'POST') {
    try {
      const {
        title,
        description,
        image_url,
        thumbnail_url,
        image_date,
        tags,
        // 支持旧格式
        imgUrl,
        imgText,
        imgDatd
      } = req.body;

      const finalImageUrl = image_url || imgUrl;
      const finalDescription = description || imgText;
      const finalImageDate = image_date || imgDatd;

      if (!finalImageUrl || !finalDescription) {
        return sendError(res, '图片URL和描述不能为空', 400);
      }

      if (!validateLength(finalDescription, 1, 200)) {
        return sendError(res, '图片描述长度应在1-200字符之间', 400);
      }

      if (finalImageDate && !validateDateFormat(finalImageDate)) {
        return sendError(res, '日期格式不正确，应为YYYY-MM-DD格式', 400);
      }

      const { data: newImage, error } = await supabase
        .from('love_images')
        .insert([{
          title: title || '',
          description: finalDescription,
          image_url: finalImageUrl,
          thumbnail_url: thumbnail_url || finalImageUrl,
          image_date: finalImageDate || new Date().toISOString().split('T')[0],
          tags: tags || []
        }])
        .select()
        .single();

      if (error) {
        console.error('创建恋爱相册记录失败:', error);
        return sendError(res, '添加图片失败', 500);
      }

      // 转换返回数据格式
      const transformedImage = {
        id: newImage.id,
        imgUrl: newImage.image_url,
        imgText: newImage.description || '',
        imgDatd: newImage.image_date,
        createTime: newImage.created_at,
        updateTime: newImage.updated_at,
        title: newImage.title || '',
        thumbnailUrl: newImage.thumbnail_url,
        tags: newImage.tags || []
      };

      return sendSuccess(res, transformedImage, '添加成功', 201);
    } catch (error) {
      console.error('Error in love images endpoint:', error);
      return sendError(res, '处理恋爱相册请求失败', 500);
    }
  }

  return sendError(res, '不支持的请求方法', 405);
}

// 恋爱清单 API
async function handleLoveList(req, res) {
  if (req.method === 'GET') {
    try {
      const { data: loveList, error } = await supabase
        .from('love_list')
        .select('*')
        .order('created_at', { ascending: true });

      if (error) {
        console.error('获取恋爱清单失败:', error);
        return sendError(res, '获取恋爱清单失败', 500);
      }

      // 转换数据格式以匹配前端期望
      const transformedData = (loveList || []).map(item => ({
        id: item.id,
        eventname: item.title,
        icon: item.is_completed ? 1 : 0,
        imgurl: '', // 可以后续添加
        description: item.description,
        category: item.category,
        completed_date: item.completed_date
      }));

      return sendSuccess(res, transformedData, '获取成功');
    } catch (error) {
      console.error('Error in love list endpoint:', error);
      return sendError(res, '处理恋爱清单请求失败', 500);
    }
  }

  if (req.method === 'POST') {
    try {
      const { title, description, category, priority } = req.body;

      if (!title) {
        return sendError(res, '标题不能为空', 400);
      }

      const { data: newItem, error } = await supabase
        .from('love_list')
        .insert([{
          title,
          description: description || '',
          category: category || 'general',
          priority: priority || 1
        }])
        .select()
        .single();

      if (error) {
        console.error('创建恋爱清单项失败:', error);
        return sendError(res, '创建清单项失败', 500);
      }

      return sendSuccess(res, newItem, '创建成功', 201);
    } catch (error) {
      console.error('Error in love list endpoint:', error);
      return sendError(res, '处理恋爱清单请求失败', 500);
    }
  }

  return sendError(res, '不支持的请求方法', 405);
}

// 时间线事件 API
async function handleTimeline(req, res) {
  if (req.method === 'GET') {
    try {
      const {
        page = 1,
        limit = 20,
        event_type = '',
        start_date = '',
        end_date = '',
        sort_by = 'date',
        sort_order = 'desc'
      } = req.query;

      const offset = (page - 1) * limit;

      let query = supabase.from('timeline_events').select('*', { count: 'exact' });

      // 事件类型筛选
      if (event_type) {
        query = query.eq('event_type', event_type);
      }

      // 日期范围筛选
      if (start_date && validateDateFormat(start_date)) {
        query = query.gte('date', start_date);
      }

      if (end_date && validateDateFormat(end_date)) {
        query = query.lte('date', end_date);
      }

      // 排序
      const ascending = sort_order === 'asc';
      if (sort_by === 'date') {
        query = query.order('date', { ascending });
      } else if (sort_by === 'created_at') {
        query = query.order('created_at', { ascending });
      } else if (sort_by === 'sort_order') {
        query = query.order('sort_order', { ascending }).order('date', { ascending: false });
      }

      // 分页
      query = query.range(offset, offset + limit - 1);

      const { data: events, error, count } = await query;

      if (error) {
        console.error('获取时间线事件失败:', error);

        // 如果数据库表不存在，返回默认数据
        if (error.code === 'PGRST116' || error.message.includes('relation') || error.message.includes('does not exist')) {
          console.log('时间线表不存在，返回默认数据');
          return sendSuccess(res, {
            list: getDefaultTimelineEvents(),
            total: 4,
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages: 1
          }, '获取成功（默认数据）');
        }

        return sendError(res, '获取时间线事件失败', 500);
      }

      return sendSuccess(res, {
        list: events || [],
        total: count || 0,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil((count || 0) / limit)
      }, '获取成功');
    } catch (error) {
      console.error('Error in timeline endpoint:', error);
      // 发生异常时也返回默认数据
      return sendSuccess(res, {
        list: getDefaultTimelineEvents(),
        total: 4,
        page: 1,
        limit: 20,
        totalPages: 1
      }, '获取成功（默认数据）');
    }
  }

  if (req.method === 'POST') {
    try {
      const {
        date,
        title,
        description,
        event_type = 'memory',
        icon = 'Calendar',
        color = 'primary',
        images = [],
        location = '',
        mood = '',
        is_featured = false,
        sort_order = 0
      } = req.body;

      if (!date || !title) {
        return sendError(res, '日期和标题不能为空', 400);
      }

      if (!validateDateFormat(date)) {
        return sendError(res, '日期格式不正确，应为YYYY-MM-DD格式', 400);
      }

      if (!validateLength(title, 1, 200)) {
        return sendError(res, '标题长度应在1-200字符之间', 400);
      }

      if (description && !validateLength(description, 0, 2000)) {
        return sendError(res, '描述长度不能超过2000字符', 400);
      }

      const { data: newEvent, error } = await supabase
        .from('timeline_events')
        .insert([{
          date,
          title,
          description: description || '',
          event_type,
          icon,
          color,
          images,
          location,
          mood,
          is_featured,
          sort_order
        }])
        .select()
        .single();

      if (error) {
        console.error('创建时间线事件失败:', error);
        return sendError(res, '创建时间线事件失败', 500);
      }

      return sendSuccess(res, newEvent, '创建成功', 201);
    } catch (error) {
      console.error('Error in timeline endpoint:', error);
      return sendError(res, '处理时间线事件请求失败', 500);
    }
  }

  return sendError(res, '不支持的请求方法', 405);
}

// 获取默认时间线事件数据（当数据库表不存在时使用）
function getDefaultTimelineEvents() {
  return [
    {
      id: 1,
      date: '2023-12-29',
      title: '我们相遇了',
      description: '2023年12月29日，我们相遇了。从那一刻起，生活变得更加美好。我们一起看过日出日落，一起走过春夏秋冬，一起创造属于我们的美好回忆。',
      event_type: 'meeting',
      icon: 'User',
      color: 'primary',
      images: [],
      location: '',
      mood: 'happy',
      is_featured: true,
      sort_order: 1,
      created_at: '2023-12-29T00:00:00Z',
      updated_at: '2023-12-29T00:00:00Z'
    },
    {
      id: 2,
      date: '2024-01-01',
      title: '新年第一天',
      description: '2024年的第一天，我们一起迎接新年的到来。许下美好的愿望，希望我们的爱情能够长长久久。',
      event_type: 'celebration',
      icon: 'Star',
      color: 'danger',
      images: [],
      location: '',
      mood: 'excited',
      is_featured: false,
      sort_order: 2,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 3,
      date: '2024-02-14',
      title: '第一个情人节',
      description: '我们一起度过的第一个情人节，虽然简单，但是充满了爱意。你说，有我在身边就是最好的礼物。',
      event_type: 'anniversary',
      icon: 'Present',
      color: 'danger',
      images: [],
      location: '',
      mood: 'romantic',
      is_featured: true,
      sort_order: 3,
      created_at: '2024-02-14T00:00:00Z',
      updated_at: '2024-02-14T00:00:00Z'
    },
    {
      id: 4,
      date: '2024-06-01',
      title: '第一次旅行',
      description: '我们的第一次旅行，虽然只是短短的周末，但是留下了很多美好的回忆。看着你开心的笑容，我觉得一切都值得。',
      event_type: 'travel',
      icon: 'Location',
      color: 'info',
      images: [],
      location: '某个美丽的地方',
      mood: 'happy',
      is_featured: false,
      sort_order: 4,
      created_at: '2024-06-01T00:00:00Z',
      updated_at: '2024-06-01T00:00:00Z'
    }
  ];
}

// 关于信息 API
async function handleAbout(req, res) {
  const aboutInfo = {
    hero: {
      title: '关于我们',
      subtitle: '毛双欢和张家伟的美好时光',
      backgroundImage: '/images/about-hero-bg.jpg'
    },
    couple: {
      boy: {
        name: '张家伟',
        nickname: '布布',
        description: '一个温暖阳光的大男孩，喜欢技术，更喜欢陪伴在你身边的每一个瞬间。用代码记录我们的爱情，用心守护我们的未来。',
        avatar: '/images/boy-avatar.jpg',
        traits: ['技术宅', '温暖', '细心', '浪漫']
      },
      girl: {
        name: '毛双欢',
        nickname: '一二',
        description: '一个可爱活泼的女孩子，喜欢花朵，喜欢浪漫，更喜欢和你一起创造美好的回忆。愿我们的爱情如花般绚烂绽放。',
        avatar: '/images/girl-avatar.jpg',
        traits: ['可爱', '活泼', '浪漫', '温柔']
      }
    },
    story: {
      title: '我们的故事',
      content: '2023年12月29日，我们相遇了。从那一刻起，生活变得更加美好。我们一起看过日出日落，一起走过春夏秋冬，一起创造属于我们的美好回忆。这个小窝记录着我们的点点滴滴，见证着我们的成长与爱情。',
      meetDate: '2023-12-29'
    },
    website: {
      description: '这是一个专属的情侣网站，记录我们的美好时光。用技术的力量保存爱情的温度，让每一个瞬间都成为永恒。',
      features: [
        '文章分享 - 记录生活点滴',
        '留言板 - 收集祝福话语',
        '恋爱相册 - 保存美好瞬间',
        '恋爱清单 - 规划未来约定',
        '关于我们 - 分享爱情故事'
      ],
      version: '1.0.0',
      author: '毛双欢 & 张家伟',
      contact: {
        email: '<EMAIL>',
        qq: '**********'
      }
    }
  };

  return sendSuccess(res, aboutInfo, '获取成功');
}

// 主路由处理器 API网关
export default async (req, res) => {
  // 处理 CORS 预检请求
  if (handleCorsPreflightRequest(req, res)) {
    return;
  }

  try {
    // 获取请求路径
    const path = req.url.split('?')[0];

    // 路由分发
    switch (true) {
      case path === '/api/health':
        return await handleHealth(req, res);
      case path === '/api/site/config':
        return await handleSiteConfig(req, res);
      case path === '/api/articles':
        return await handleArticles(req, res);
      case path.startsWith('/api/articles/') && path !== '/api/articles':
        // 处理文章的动态路由: /api/articles/:id 和 /api/articles/:id/related
        const articlePath = path.replace('/api/articles/', '');
        return await handleArticles(req, res, articlePath);
      case path === '/api/messages':
        return await handleMessages(req, res);
      case path === '/api/love-images':
        return await handleLoveImages(req, res);
      case path === '/api/love-list':
        return await handleLoveList(req, res);
      case path === '/api/timeline':
        return await handleTimeline(req, res);
      case path === '/api/about':
        return await handleAbout(req, res);
      default:
        return sendNotFound(res, `API 端点不存在: ${path}`);
    }

  } catch (error) {
    console.error(`API 处理错误: ${error.message}`, error);
    return sendError(res, '服务器内部错误', 500);
  }
};
