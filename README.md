
```
mshandzjw-vue
├─ .env
├─ .env.example
├─ .kiro
│  └─ specs
├─ .prettierrc.cjs
├─ api
│  ├─ about
│  ├─ articles
│  │  └─ [id]
│  ├─ index.js
│  ├─ love-images
│  ├─ love-list
│  ├─ messages
│  ├─ qq
│  ├─ site
│  └─ _utils
├─ index.html
├─ package.json
├─ pnpm-lock.yaml
├─ public
│  ├─ images
│  │  ├─ comment.svg
│  │  ├─ diandi.svg
│  │  ├─ gallary.svg
│  │  ├─ headCover.jpg
│  │  ├─ like.png
│  │  ├─ loveimg.png
│  │  ├─ red_menu.svg
│  │  └─ yellow_heart.svg
│  └─ vite.svg
├─ README.md
├─ src
│  ├─ api
│  │  └─ index.js
│  ├─ App.vue
│  ├─ assets
│  │  └─ vue.svg
│  ├─ components
│  │  ├─ ArticleCard.vue
│  │  ├─ Footer.vue
│  │  └─ Header.vue
│  ├─ composables
│  │  └─ useResponsive.js
│  ├─ constants
│  │  └─ index.js
│  ├─ main.js
│  ├─ router
│  │  └─ index.js
│  ├─ stores
│  │  └─ site.js
│  ├─ styles
│  │  ├─ mixins.css
│  │  ├─ responsive.css
│  │  ├─ touch.css
│  │  └─ variables.css
│  ├─ utils
│  │  └─ index.js
│  └─ views
│     ├─ About.vue
│     ├─ ArticleDetail.vue
│     ├─ Home.vue
│     ├─ Leaving.vue
│     ├─ Little.vue
│     ├─ LoveImg.vue
│     └─ LoveList.vue
├─ vercel.json
└─ vite.config.js

```