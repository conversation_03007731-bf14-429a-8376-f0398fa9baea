{"name": "mshandzjw-love-site", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@supabase/supabase-js": "^2.53.0", "axios": "^1.6.0", "dayjs": "^1.11.13", "dompurify": "^3.2.6", "element-plus": "^2.10.2", "pinia": "^3.0.3", "vue": "^3.5.12", "vue-router": "4"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.4", "prettier": "^3.6.2", "vite": "^5.4.10"}}