<template>
  <div class="footer-warp" v-if="siteStore.config.copyright || siteStore.config.icp">
    <div class="footer">
      <p v-if="siteStore.config.icp">
        <a :href="'https://beian.miit.gov.cn/#/Integrated/index'" target="_blank">{{ siteStore.config.icp }}</a>
      </p>
      <p v-if="siteStore.config.copyright">{{ siteStore.config.copyright }}</p>
      <p v-else>© 2024 {{ siteStore.config.title }} | Powered by Vue3</p>
    </div>
  </div>
</template>

<script setup>
import { useSiteStore } from '../stores/site'

const siteStore = useSiteStore()
</script>

<style scoped>
.footer-warp {
  margin-top: 4rem;
  background: #f8f8f8;
}

.footer-warp .footer {
  padding: 2rem;
  text-align: center;
  font-family: 'Noto Serif SC', serif;
  font-weight: 400;
}

.footer-warp .footer p {
  line-height: 2.5rem;
  margin: 0;
  color: #666;
}

.footer-warp .footer a {
  color: #666;
  text-decoration: none;
}

.footer-warp .footer a:hover {
  color: #10bbff;
}
</style>
