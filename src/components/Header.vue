<template>
	<div class="home-page">
		<!-- 头部导航 -->
		<div class="header-wrap">
			<div class="header">
				<div class="logo">
					<h1>
						<a  href="/">{{ siteStore.config.logo }}</a>
					</h1>
				</div>
				<div class="word">
					<span>{{ siteStore.config.writing || '喜欢花 喜欢浪漫 喜欢你~' }}</span>
				</div>
			</div>
		</div>

		<!-- 背景和情侣头像区域 -->
		<div class="bg-wrap">
			<div class="bg-img">
				<div class="central central-800">
					<div class="middle" :class="{ 'animated fadeInDown': siteStore.config.animation === '1' }">
						<div class="img-male">
							<img :src="boyAvatar" draggable="false" alt="男生头像" />
							<span>{{ siteStore.config.boy || 'Ki' }}</span>
						</div>
						<div class="love-icon">
							<img src="/images/like.png" draggable="false" alt="爱心" />
						</div>
						<div class="img-female">
							<img :src="girlAvatar" draggable="false" alt="女生头像" />
							<span>{{ siteStore.config.girl || 'Li' }}</span>
						</div>
					</div>
				</div>

				<!-- 波浪效果 -->
				<div class="waves">
					<svg width="100%" height="100%" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto">
						<defs>
							<path id="gentle-wave" d="m-160 44c30 0 58-18 88-18s 58 18 88 18 58-18 88-18 58 18 88 18 v44h-352z" />
						</defs>
						<g class="parallax">
							<use xlink:href="#gentle-wave" x="48" y="0" fill="rgba(255,255,255,0.7)" />
							<use xlink:href="#gentle-wave" x="48" y="3" fill="rgba(255,255,255,0.5)" />
							<use xlink:href="#gentle-wave" x="48" y="5" fill="rgba(255,255,255,0.3)" />
							<use xlink:href="#gentle-wave" x="48" y="7" fill="#fff" />
						</g>
					</svg>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { useSiteStore } from '@/stores/site';

const router = useRouter();
const siteStore = useSiteStore();
// 头像计算属性
const boyAvatar = computed(() => {
	const qq = siteStore.config.boyQQ;
	return `https://q1.qlogo.cn/g?b=qq&nk=${qq}&s=640`;
});

const girlAvatar = computed(() => {
	const qq = siteStore.config.girlQQ;
	return `https://q1.qlogo.cn/g?b=qq&nk=${qq}&s=640`;
});
</script>

<style scoped>
.home-page {
	min-height: 50vh;
}
/* 顶部导航 */
.header-wrap {
  width: 100%;
  height: 4.5rem;
  display: flex;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1;
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  background: #ffffff1f;
  color: #707070;
}
.header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-around;
  flex-wrap: nowrap;
}
.logo a {
  font-size: 1.5rem;
  font-family: 'Noto Serif SC', serif;
  font-weight: 700;
	color: #fdfdfd;
}
.word {
  font-family: 'Noto Serif SC', serif;
  font-weight: 400;
  font-size: 1.1rem;
  width: 60%;
  text-align: right;
	color: #fdfdfd;
}
</style>
