<template>
  <div class="love-img-container">
    <h4 class="page-title">记录下你的最美瞬间</h4>
    
    <div class="image-grid">
      <el-card 
        v-for="item in loveImages" 
        :key="item.id"
        class="love-card"
        :body-style="{ padding: '0' }"
        shadow="hover"
      >
        <div class="love-img-content">
          <img 
            :src="item.imgUrl" 
            :alt="item.imgText"
            class="love-image"
          />
          
          <div class="image-info">
            <i class="image-date">Date：{{ item.imgDate }}</i>
            <span class="image-text">{{ item.imgText }}</span>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const loveImages = ref([])

// 模拟数据，实际项目中应该从API获取
const mockData = [
  {
    id: 1,
    imgUrl: '/public/images/sample1.jpg',
    imgText: '下班经过的路上，正好夕阳斜照着了',
    imgDate: '2024-11-07'
  },
  {
    id: 2,
    imgUrl: '/public/images/sample2.jpg', 
    imgText: '今天钓到了一条大鱼',
    imgDate: '2024-11-07'
  },
  {
    id: 3,
    imgUrl: '/public/images/sample3.jpg',
    imgText: '入手两公路车已经好几个月了，走了不少长途路',
    imgDate: '2024-11-09'
  }
]

onMounted(() => {
  // 这里应该调用API获取数据
  loveImages.value = mockData
})

// 如果需要从后端API获取数据，可以使用这个函数
const fetchLoveImages = async () => {
  try {
    // const response = await fetch('/api/love-images')
    // const data = await response.json()
    // loveImages.value = data
  } catch (error) {
    console.error('获取图片数据失败:', error)
  }
}
</script>

<style scoped>
.love-img-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.page-title {
  text-align: center;
  font-family: 'Noto Serif SC', serif;
  font-weight: 700;
  color: #333;
  margin-bottom: 2rem;
  font-size: 2.5rem;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  justify-items: center;
}

.love-card {
  width: 100%;
  max-width: 350px;
  border-radius: 1.5rem;
  border: 1px solid rgba(208, 206, 206, 0.4);
  transition: all 0.3s ease;
  background: #fafafa;
}

.love-card:hover {
  background: #494949;
  cursor: pointer;
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.love-img-content {
  padding: 1.3rem 1.3rem 1.5rem;
}

.love-image {
  width: 100%;
  height: 25rem;
  border-radius: 1rem;
  object-fit: cover;
  box-shadow: 0 0px 30px rgba(133, 125, 125, 0.47);
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.love-card:hover .love-image {
  transform: translateY(-5px);
  box-shadow: 0 5px 20px rgba(59, 59, 59, 0.68);
}

.image-info {
  padding: 0 1rem;
}

.image-date {
  display: block;
  text-align: right;
  font-style: normal;
  font-family: 'Noto Serif SC', serif;
  font-weight: 400;
  color: #999;
  padding-bottom: 0.8rem;
  margin-bottom: 0.5rem;
  border-bottom: 1px dashed #d7d7d7;
  transition: color 0.3s ease;
}

.image-text {
  font-family: 'Noto Serif SC', serif;
  font-weight: 400;
  font-size: 1.05rem;
  color: #454040;
  letter-spacing: 1px;
  line-height: 1.5em;
  display: block;
  transition: color 0.3s ease;
}

.love-card:hover .image-text {
  color: #e2e2e2;
}

.love-card:hover .image-date {
  color: #bbb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .love-img-container {
    padding: 1rem;
  }
  
  .image-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .love-card {
    max-width: 100%;
  }
  
  .love-image {
    height: 20rem;
  }
}

@media (max-width: 480px) {
  .love-image {
    height: 18rem;
  }
  
  .love-img-content {
    padding: 1rem;
  }
}
</style>