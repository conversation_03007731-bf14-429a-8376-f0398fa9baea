<template>
  <div class="leaving-page">
    <div class="central central-800">
      <div class="title">
        <h1>在这里写下我们的留言祝福</h1>
      </div>

      <!-- 留言统计 -->
      <div class="message-stats">
        <h3>已收到 <span class="count">{{ messageCount }}</span> 条祝福留言
          <span class="limit-info">（显示最新 {{ displayLimit }} 条）</span>
        </h3>
      </div>

      <!-- 浮动操作按钮 -->
      <el-button 
        type="primary" 
        class="fab" 
        @click="openFormDialog"
        size="large"
        circle
      >
        <img src="/images/comment.svg" alt="留言" style="width: 24px; height: 24px;" />
      </el-button>

      <!-- 留言表单对话框 -->
      <el-dialog
        v-model="dialogVisible"
        title="留下你的祝福和话语"
        width="90%"
        :before-close="handleClose"
        class="message-dialog"
        :fullscreen="isMobile"
        append-to-body
        center
      >
        <el-form :model="messageForm" :rules="formRules" ref="messageFormRef">
          <div class="input-row">
            <div class="avatar-preview">
              <img
                :src="previewAvatar"
                alt="头像预览"
                class="avatar"
              />
            </div>
            <div class="input-fields">
              <el-form-item prop="qq">
                <el-input
                  v-model="messageForm.qq"
                  placeholder="请输入QQ号码(不会显示您的QQ号)"
                  @blur="fetchQQInfo"
                  :loading="qqLoading"
                />
              </el-form-item>
              <el-form-item prop="name">
                <el-input
                  v-model="messageForm.name"
                  placeholder="昵称（选填，默认自动获取）"
                  :disabled="qqLoading"
                  @input="isNameManuallySet = true"
                />
              </el-form-item>
            </div>
          </div>
          <el-form-item prop="text">
            <el-input
              v-model="messageForm.text"
              type="textarea"
              :rows="6"
              placeholder="请输入您的留言内容（恶意留言会封禁IP...）"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="handleCloseDialog">取消</el-button>
            <el-button
              type="primary"
              :loading="submitting"
              @click="submitMessage"
            >
              提交留言
            </el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 留言列表 -->
      <div class="messages-section">
        <div v-if="loading" class="loading">
          <el-loading :loading="true" text="加载留言中..." />
        </div>

        <div v-else class="messages-container">
          <div
            v-for="(message, index) in messages"
            :key="message.id"
            class="message-card"
            :class="{ 'animate__animated animate__fadeInUp': siteStore.config.animation === '1' }"
            :style="{ animationDelay: `${index * 0.1}s` }"
          >
            <div class="message-header">
              <div class="user-info">
                <img
                  :src="`https://q1.qlogo.cn/g?b=qq&nk=${message.qq}&s=100`"
                  :alt="message.name"
                  class="avatar"
                  @error="handleAvatarError"
                />
                <span class="username">{{ message.name }}</span>
              </div>
              <div class="time-location">
                <span>{{ formatTime(message.time) }} • {{ message.city || '未知' }}</span>
              </div>
            </div>

            <hr class="separator"/>

            <div class="message-body">
              <div class="message-content">
                <p>{{ message.text }}</p>
              </div>
            </div>
          </div>

          <div v-if="messages.length === 0" class="empty-state">
            <el-icon size="60" color="#ccc"><ChatDotSquare /></el-icon>
            <p>还没有留言，快来抢沙发吧～</p>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ChatDotSquare } from '@element-plus/icons-vue'
import { getMessages, submitMessage as submitMessageApi} from '../api'
import { useSiteStore } from '../stores/site'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import updateLocale from 'dayjs/plugin/updateLocale'
import 'dayjs/locale/zh-cn'
import axios from 'axios';

dayjs.extend(relativeTime)
dayjs.extend(updateLocale)

dayjs.updateLocale('zh-cn', {
  relativeTime: {
    future: '%s内',
    past: '%s前',
    s: '几秒',
    m: '1分钟',
    mm: '%d分钟',
    h: '1小时',
    hh: '%d小时',
    d: '1天',
    dd: '%d天',
    M: '1月',
    MM: '%d月',
    y: '1年',
    yy: '%d年'
  }
})

dayjs.locale('zh-cn')

const siteStore = useSiteStore()

const mockMessagesData = {
  total: 132,
  messages: [
    {
      id: 1,
      name: 'Ki.',
      qq: '333999',
      text: '测试留言板20250227',
      time: dayjs().subtract(4, 'month').toISOString(),
      city: '广东'
    },
    {
      id: 2,
      name: '大大的我',
      qq: '80001',
      text: '哎吆哎吆哎吆',
      time: dayjs().subtract(4, 'month').toISOString(),
      city: '江苏'
    },
    {
      id: 3,
      name: '小小JENNIE',
      qq: '23333',
      text: '好看丫',
      time: dayjs().subtract(4, 'month').toISOString(),
      city: '湖北'
    },
    {
      id: 4,
      name: '是阿卷啊',
      qq: '666666',
      text: '哇，好好看',
      time: dayjs().subtract(5, 'month').toISOString(),
      city: '上海'
    }
  ]
}

const messages = ref([])
const messageCount = ref(0)
const displayLimit = ref(10)
const loading = ref(true)
const submitting = ref(false)
const qqLoading = ref(false)
const dialogVisible = ref(false)
const isNameManuallySet = ref(false)

const isMobile = computed(() => {
  if (typeof window !== 'undefined') {
    return window.innerWidth < 768
  }
  return false
})

// 留言表单
const messageForm = ref({
  qq: '',
  name: '',
  text: ''
})

const messageFormRef = ref()

// 表单验证规则
const formRules = {
  qq: [
    { required: true, message: '请输入QQ号码', trigger: 'blur' },
    { pattern: /^\d{5,11}$/, message: 'QQ号码格式不正确', trigger: 'blur' }
  ],
  text: [
    { required: true, message: '请输入留言内容', trigger: 'blur' },
    { min: 1, max: 500, message: '留言内容长度为 1 到 500 个字符', trigger: 'blur' }
  ]
}

// 预览头像
const previewAvatar = computed(() => {
  return messageForm.value.qq 
    ? `https://q1.qlogo.cn/g?b=qq&nk=${messageForm.value.qq}&s=100`
    : 'https://q1.qlogo.cn/g?b=qq&nk=1234567&s=100'
})

// 打开表单对话框
const openFormDialog = () => {
  isNameManuallySet.value = false;
  messageForm.value = { qq: '', name: '', text: '' };
  if (messageFormRef.value) {
    messageFormRef.value.resetFields();
  }
  dialogVisible.value = true;
};

// 关闭对话框处理
const handleCloseDialog = () => {
  if (messageForm.value.qq || messageForm.value.text) {
    ElMessageBox.confirm('您有未提交的内容，确定要关闭吗？')
      .then(() => {
        dialogVisible.value = false
      })
      .catch(() => {})
  } else {
    dialogVisible.value = false
  }
}

const handleClose = (done) => {
  handleCloseDialog()
  // done() is not called intentionally to let handleCloseDialog control visibility
}

// 获取留言列表
const fetchMessages = async () => {
  try {
    loading.value = true
    // 使用模拟数据
    const response = await getMessages(displayLimit.value)
    messages.value = response.data.messages || mockMessagesData.messages
    messageCount.value = response.data.total || mockMessagesData.total
  } catch (error) {
    console.error('获取留言失败:', error)
    messages.value = []
    messageCount.value = 0
  } finally {
    loading.value = false
  }
}

// 获取QQ信息
const fetchQQInfo = async () => {
  if (!messageForm.value.qq || !/^\d{5,11}$/.test(messageForm.value.qq)) {
    return
  }

  try {
    qqLoading.value = true
		messageForm.value.name = axios.get('http://api.mmp.cc/api/qqname', {
			params: {
				qq: messageForm.value.qq
			}
		})
		.then(response => {
			messageForm.value.name = response.data.data.name
		})
  } catch (error) {
    console.error('获取QQ信息失败:', error)
  } finally {
    qqLoading.value = false
  }
}

// 提交留言
const submitMessage = async () => {
  if (!messageFormRef.value) return

  try {
    await messageFormRef.value.validate()
    
    submitting.value = true
    
    const response = await submitMessageApi({
      qq: messageForm.value.qq,
      name: messageForm.value.name,
      text: messageForm.value.text
    })
		if (response.status ===200)
    	ElMessage.success('留言提交成功！')
    dialogVisible.value = false
    
    // 重新获取留言列表
    await fetchMessages()
    
  } catch (error) {
    // validation error will be caught here
    if (error) {
      console.error('提交留言失败:', error)
      ElMessage.error('留言提交失败，请检查表单内容')
    }
  } finally {
    submitting.value = false
  }
}

// 格式化时间
const formatTime = (timeStr) => {
  return dayjs(timeStr).fromNow()
}

// 头像加载错误处理
const handleAvatarError = (event) => {
  event.target.src = 'https://q1.qlogo.cn/g?b=qq&nk=1234567&s=100'
}

onMounted(() => {
  fetchMessages()
})
</script>

<style scoped>
.leaving-page {
  min-height: 100vh;
  padding: 40px 0;
  position: relative;
}

.fab {
  position: fixed;
  bottom: 90px;
  right: 30px;
  z-index: 100;
  width: 60px;
  height: 60px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

@media (max-width: 768px) {
  .fab {
    bottom: 80px;
    right: 20px;
  }
}

.title {
  text-align: center;
  margin-bottom: 40px;
}

.title h1 {
  color: #171717;
  font-size: 2.5rem;
  margin-bottom: 10px;
  text-shadow: 0 2px 10px rgba(0,0,0,0.3);
  font-weight: 600;
}

.title p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
}

.message-stats {
  text-align: center;
  margin-bottom: 40px;
}

.message-stats h3 {
  color: #000000;
  font-size: 1.2rem;
  text-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

.count {
  color: #FFD700;
  font-weight: bold;
  font-size: 3rem;
}

.limit-info {
  font-size: 0.9rem;
  opacity: 0.8;
	color: rgba(161, 161, 161, 0.8);
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.messages-container {
  margin-bottom: 40px;
}

.message-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 20px 25px;
  margin-bottom: 20px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.message-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.time-location {
  background-color: #eaf2ff;
  color: #409eff;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.location {
  margin-left: 10px;
  color: #999;
}

.message-body {
  margin-top: 15px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.avatar {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  object-fit: cover;
  border: none;
  box-shadow: none;
}

.username {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
}

.message-content {
  color: #555;
  line-height: 1.7;
  word-wrap: break-word;
  padding: 0;
  background: none;
}

.separator {
  border: none;
  border-top: 1px dotted #e0e0e0;
  margin: 0;
}

.message-dialog .input-row {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  margin-bottom: 20px;
}

.message-dialog .avatar-preview {
  flex-shrink: 0;
  text-align: center;
}

.message-dialog .avatar-preview .avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
}

.message-dialog .input-fields {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.message-dialog .input-fields .el-form-item {
  margin-bottom: 0;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.empty-state p {
  font-size: 1.1rem;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .title h1 {
    font-size: 2rem;
  }
  
  .message-card {
    padding: 20px;
  }
  
  .message-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .message-body {
    flex-direction: column;
    gap: 10px;
  }
  
  .user-info {
    flex-direction: row;
    align-items: center;
    min-width: auto;
    gap: 10px;
  }
  
  .avatar {
    width: 50px;
    height: 50px;
  }
  
  .message-dialog .input-row {
    flex-direction: column;
    gap: 15px;
    align-items: center;
  }
  
  .message-dialog .input-fields {
    width: 100%;
  }

  .message-dialog .avatar-preview {
    margin: 0 auto 15px;
  }
}
</style>
