<template>
  <div class="article-detail-page">
    <!-- Loading state -->
    <div v-if="loading" class="loading-container">
      <el-skeleton animated>
        <template #template>
          <el-skeleton-item variant="h1" style="width: 80%; height: 40px; margin-bottom: 20px;" />
          <el-skeleton-item variant="text" style="width: 60%; margin-bottom: 20px;" />
          <el-skeleton-item variant="image" style="width: 100%; height: 200px; margin-bottom: 20px;" />
          <el-skeleton-item variant="p" style="width: 100%;" />
          <el-skeleton-item variant="p" style="width: 95%;" />
        </template>
      </el-skeleton>
    </div>

    <!-- Article content -->
    <div v-else-if="article" class="article-container">
      <!-- Back button -->
      <el-button @click="returnList" :icon="ArrowLeft" type="primary" plain class="back-btn">
        返回列表
      </el-button>

      <!-- Article header -->
      <div class="article-header">
        <h1 class="article-title">{{ article.title }}</h1>
        <div class="article-meta">
          <span><el-icon><User /></el-icon> {{ article.author }}</span>
          <span><el-icon><Calendar /></el-icon> {{ formatDate(article.created_at) }}</span>
        </div>
        <div v-if="article.tags && article.tags.length" class="article-tags">
          <el-tag v-for="tag in article.tags" :key="tag" size="small">{{ tag }}</el-tag>
        </div>
      </div>

      <!-- Article content -->
      <div class="article-content">
        <div class="content-body" v-html="sanitizedContent"></div>
      </div>

      <!-- Related articles -->
      <div class="related-articles" v-if="relatedArticles.length > 0">
        <h3 class="related-title">相关回忆</h3>
        <div class="related-list">
          <div 
            v-for="item in relatedArticles" 
            :key="item.id" 
            class="related-item"
            @click="goToArticle(item.id)"
          >
            <div class="related-content">
              <div class="related-article-title">{{ item.title }}</div>
            </div>
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- Article not found -->
    <div v-else class="not-found">
      <el-icon size="60"><Document /></el-icon>
      <h3>文章不存在</h3>
      <p>抱歉，您访问的文章可能已被删除或不存在</p>
      <el-button @click="returnList" type="primary">返回列表</el-button>
    </div>

    <!-- Back to top -->
    <el-backtop />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  ArrowLeft, 
  ArrowRight, 
  User, 
  Calendar, 
  Document
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useSiteStore } from '../stores/site'
import { getArticleById, getRelatedArticles } from '../api/index.js'
import dayjs from 'dayjs'
import DOMPurify from 'dompurify'

const route = useRoute()
const router = useRouter()
const siteStore = useSiteStore()

// Reactive data
const article = ref(null)
const relatedArticles = ref([])
const loading = ref(true)

// Mock 数据（与 Little.vue 保持一致）
const mockArticles = [
  {
    id: 1,
    title: '春日里的第一次约会',
    content: '今天和她一起去了公园，看着樱花飞舞，感受着春天的气息。她穿着淡粉色的裙子，笑容如花般灿烂。我们在小径上慢慢走着，聊着彼此的梦想和未来。那一刻，我觉得时间都静止了，只有她和我，还有满树的樱花见证着这美好的时光。<br><br>春风轻柔地吹过，花瓣如雪花般飘洒在我们身上。她伸出手接住一片樱花，笑着说这是春天给我们的礼物。我看着她的笑容，心里满满的都是幸福。我们找了一个长椅坐下，她靠在我的肩膀上，我们就这样静静地看着远山如黛，听着鸟儿的啁啾声。<br><br>这样的时光，简单却珍贵。没有华丽的言辞，没有昂贵的礼物，只有两颗相爱的心在这个春日里慢慢靠近。我想，这就是爱情最美的样子吧。',
    author: '毛双欢',
    category: 'sweet-moment',
    tags: ['约会', '春天', '樱花', '美好'],
    created_at: dayjs().subtract(2, 'day').toISOString(),
  },
  {
    id: 2,
    title: '第一次为她做早餐',
    content: '今天早上特地早起，想给她一个惊喜。虽然厨艺不精，但还是认真地煎了鸡蛋，烤了面包，还准备了她最爱的草莓酸奶。看到她睡眼惺忪地走出房间，然后惊喜地看着桌上的早餐，那个笑容真的让我觉得一切都是值得的。她说这是她吃过最美味的早餐。<br><br>其实我紧张了好久，生怕把鸡蛋煎糊了或者面包烤焦了。但看到她开心的样子，我觉得所有的努力都是值得的。她夸我说，爱心调料是世界上最好的调味品。那一刻，我的心里比蜜还甜。<br><br>从今以后，我想每个周末都为她准备早餐，看着她满足的表情，就是我最大的幸福。',
    author: '张家伟',
    category: 'daily-life',
    tags: ['早餐', '惊喜', '爱心', '美好'],
    created_at: dayjs().subtract(5, 'day').toISOString(),
  },
  {
    id: 3,
    title: '周末的咖啡时光',
    content: '周末的下午，阳光透过咖啡厅的落地窗洒在桌子上，点了一杯拿铁和她最爱的提拉米苏。我们坐在靠窗的位置，看着窗外匆忙的行人，聊着这一周发生的趣事。咖啡的香气弥漫在空气中，时光在这一刻变得格外温柔。<br><br>她总是能发现生活中有趣的细节，比如路过的小狗、飞过的鸽子、还有老爷爷遛鸟的可爱场景。听她讲这些小事，我觉得平凡的生活也变得生动起来。<br><br>午后的阳光正好，她的侧脸在光影中显得特别美丽。我想，这样的时光如果能够永远持续下去该有多好。',
    author: '毛双欢',
    category: 'sweet-moment',
    tags: ['咖啡', '周末', '甜品', '温柔'],
    created_at: dayjs().subtract(1, 'week').toISOString(),
  },
  {
    id: 4,
    title: '第一次一起做意大利面',
    content: '今天我们决定一起做晚餐，选择了意大利面。虽然两个人都是厨房新手，但一起摸索的过程特别有趣。她负责煮面条，我来调制酱汁，虽然厨房变得有点乱，但看着她认真的样子，心里满满的都是幸福。最后的成果虽然卖相一般，但味道意外地不错！<br><br>我们在厨房里忙忙碌碌，她时不时会问我放多少盐，我也会请教她面条煮多久。偶尔我们会因为小事争论，但很快就会相视而笑。厨房里飘着面条的香味，还有我们的笑声。<br><br>吃着自己做的意大利面，虽然味道可能比不上餐厅，但心里的满足感是无法比拟的。因为这是我们一起努力的成果，充满了爱的味道。',
    author: '张家伟',
    category: 'food-story',
    tags: ['意大利面', '一起做饭', '幸福', '回忆'],
    created_at: dayjs().subtract(10, 'day').toISOString(),
  },
  {
    id: 5,
    title: '我们的恋爱一周年',
    content: '今天是我们在一起的一周年纪念日，时间过得真快。还记得当初第一次见面时的紧张和兴奋，现在的我们已经能够很自然地在一起，分享彼此的快乐和烦恼。晚上我们去了第一次约会的那家餐厅，点了同样的菜，回忆起这一年来的点点滴滴。感恩能够遇见你，期待我们更美好的未来。<br><br>这一年里，我们一起经历了很多第一次：第一次旅行、第一次吵架、第一次和解、第一次说我爱你。每一个回忆都是珍贵的，每一个瞬间都值得珍藏。<br><br>看着她眼中的温柔，我知道选择她是我这辈子最正确的决定。希望未来的每一年，我们都能这样幸福地在一起。',
    author: '毛双欢',
    category: 'anniversary',
    tags: ['纪念日', '一周年', '回忆', '感恩'],
    created_at: dayjs().subtract(3, 'day').toISOString(),
  },
  {
    id: 6,
    title: '雨天里的小确幸',
    content: '今天突然下起了大雨，我们没有带伞，只好在便利店的屋檐下避雨。看着雨滴打在地面上溅起的小水花，听着雨声的节拍，突然觉得这样的时光也很美好。她说，和你在一起，连下雨天都变得浪漫了。那一刻，我觉得自己是世界上最幸福的人。<br><br>雨水冲刷着街道，空气变得清新。我们就这样站在屋檐下，看着匆忙躲雨的路人，听着雨声的交响曲。她突然指着天空说，你看，雨停了会有彩虹的。虽然最后没有看到彩虹，但她的话已经是我心中最美的彩虹。<br><br>有时候，最浪漫的不是精心准备的惊喜，而是这种突如其来的小确幸。和你在一起，每一个平凡的瞬间都变得特别。',
    author: '张家伟',
    category: 'sweet-moment',
    tags: ['雨天', '浪漫', '小确幸', '幸福'],
    created_at: dayjs().subtract(2, 'week').toISOString(),
  },
  {
    id: 7,
    title: '计划我们的第一次旅行',
    content: '最近我们开始计划第一次一起出去旅行，目标是去海边看日出。查攻略、订酒店、规划路线，每一个环节都让我们充满期待。虽然还在准备阶段，但光是想象我们一起看海、一起在沙滩上漫步的场景，就让人兴奋不已。希望这次旅行能成为我们美好的回忆。<br><br>我们在网上看了很多攻略，讨论着要住海景房还是民宿，要去哪些景点，要吃什么当地美食。她认真地做着笔记，我负责查交通路线。虽然只是在计划阶段，但我们都已经沉浸在旅行的美好想象中。<br><br>我已经开始期待那个清晨，我们手牵手站在海边，看着太阳从海平线升起，听着海浪的声音。那一定会是我们永远难忘的回忆。',
    author: '毛双欢',
    category: 'future-plan',
    tags: ['旅行计划', '海边', '日出', '期待'],
    created_at: dayjs().subtract(4, 'day').toISOString(),
  },
  {
    id: 8,
    title: '第一次一起看电影',
    content: '今晚我们一起去电影院看了一部爱情喜剧，她笑得很开心，我也被她的笑声感染了。电影散场后，我们在商场里逛了很久，买了一些小零食，然后坐在楼下的长椅上聊天到很晚。这样简单的快乐，让我觉得很幸福。有你在身边，平凡的日子也变得特别有意义。<br><br>电影很有趣，但我发现自己更多时候在看她的表情。她被逗笑的时候，眼睛会弯成月牙；她被感动的时候，会不自觉地抓紧我的手。这些小细节比电影本身更吸引我。<br><br>走在回家的路上，她突然说想要每个月都和我一起看一部电影。我立刻答应了，因为和她在一起的每一刻都是我想要珍藏的回忆。',
    author: '张家伟',
    category: 'sweet-moment',
    tags: ['电影', '约会', '快乐', '陪伴'],
    created_at: dayjs().subtract(6, 'day').toISOString(),
  }
]

// Computed properties
const sanitizedContent = computed(() => {
  if (!article.value?.content) return ''
  
  // Use DOMPurify to sanitize HTML content
  return DOMPurify.sanitize(article.value.content, {
    ALLOWED_TAGS: [
      'p', 'br', 'strong', 'em', 'u', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
      'ul', 'ol', 'li', 'blockquote', 'code', 'pre', 'a', 'img', 'table',
      'thead', 'tbody', 'tr', 'th', 'td', 'div', 'span', 'hr'
    ],
    ALLOWED_ATTR: [
      'href', 'src', 'alt', 'title', 'class', 'id', 'style', 'target',
      'width', 'height', 'align'
    ]
  })
})

// Format date
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  return dayjs(dateStr).format('YYYY年MM月DD日 HH:mm')
}

// Get article details
const fetchArticle = async () => {
  try {
    loading.value = true
    const articleId = parseInt(route.params.id)
    
    if (!articleId) {
      throw new Error('文章ID不能为空')
    }

    // 首先尝试从 API 获取文章
    try {
      const response = await getArticleById(articleId)
      
      if (response && response.code === 200 && response.data) {
        article.value = response.data
        
        // 获取相关文章
        try {
          const relatedResponse = await getRelatedArticles(articleId, 3)
          if (relatedResponse && relatedResponse.code === 200 && relatedResponse.data) {
            relatedArticles.value = relatedResponse.data
          } else {
            // 相关文章 API 失败，从 Mock 数据中获取同分类文章
            const related = mockArticles
              .filter(item => item.id !== articleId && item.category === article.value.category)
              .slice(0, 3)
            relatedArticles.value = related
          }
        } catch (relatedError) {
          console.warn('获取相关文章失败，使用 Mock 数据:', relatedError)
          const related = mockArticles
            .filter(item => item.id !== articleId && item.category === article.value.category)
            .slice(0, 3)
          relatedArticles.value = related
        }
      } else {
        throw new Error('API 返回数据格式错误')
      }
    } catch (apiError) {
      console.warn('API 获取文章失败，使用 Mock 数据:', apiError)
      
      // API 失败，使用 Mock 数据
      const foundArticle = mockArticles.find(item => item.id === articleId)
      
      if (foundArticle) {
        article.value = foundArticle
        
        // 获取相关文章 (同分类的其他文章)
        const related = mockArticles
          .filter(item => item.id !== articleId && item.category === foundArticle.category)
          .slice(0, 3)
        relatedArticles.value = related
      } else {
        article.value = null
      }
    }
    
    if (article.value) {
      // Update page title
      document.title = `${article.value.title} - 我们的小窝`
      
      // Scroll to top
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }
    
  } catch (error) {
    console.error('获取文章详情失败:', error)
    article.value = null
  } finally {
    loading.value = false
  }
}

// Go back
const returnList = () => {
  router.replace('/little')
}

const goToArticle = (id) => {
  router.push(`/article/${id}`)
}

// Watch route changes
watch(() => route.params.id, (newId) => {
  if (newId) {
    fetchArticle()
  }
})

// Lifecycle hooks
onMounted(() => {
  fetchArticle()
})
</script>

<style scoped>
.article-detail-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: var(--spacing-2xl);
  position: relative;
}

.article-detail-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: linear-gradient(to right, rgba(255, 255, 255, 0.1) 1px, transparent 1px), 
                    linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 1.5rem 1.5rem;
  opacity: 0.3;
  pointer-events: none;
}

/* Loading container */
.loading-container {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-2xl);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: var(--backdrop-blur-lg);
  border-radius: var(--radius-large);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-medium);
}

/* Article container */
.article-container {
  max-width: 800px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: var(--backdrop-blur-lg);
  border-radius: var(--radius-large);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-medium);
  padding: var(--spacing-2xl);
  position: relative;
  z-index: 1;
}

/* Back button */
.back-btn {
  margin-bottom: var(--spacing-xl);
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  color: white;
}

.back-btn:hover {
  background: linear-gradient(135deg, #5a6fd8, #6a4190);
}

/* Article header */
.article-header {
  margin-bottom: var(--spacing-2xl);
  padding-bottom: var(--spacing-xl);
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

.article-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
}

.article-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.article-meta span {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: linear-gradient(135deg, #667eea15, #764ba215);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-medium);
}

.article-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.article-tags .el-tag {
  background: linear-gradient(135deg, #667eea20, #764ba220);
  border-color: transparent;
  color: var(--text-primary);
}

/* Article content */
.article-content {
  margin-bottom: var(--spacing-2xl);
}

.content-body {
  line-height: var(--line-height-relaxed);
  font-size: var(--font-size-base);
  color: var(--text-primary);
}

.content-body :deep(h1),
.content-body :deep(h2),
.content-body :deep(h3) {
  margin: var(--spacing-xl) 0 var(--spacing-md);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.content-body :deep(p) {
  margin-bottom: var(--spacing-lg);
}

.content-body :deep(br) {
  line-height: var(--spacing-md);
}

.content-body :deep(img) {
  max-width: 100%;
  height: auto;
  border-radius: var(--radius-medium);
  margin: var(--spacing-lg) 0;
  box-shadow: var(--shadow-light);
}

.content-body :deep(blockquote) {
  border-left: 4px solid #667eea;
  padding-left: var(--spacing-lg);
  margin: var(--spacing-lg) 0;
  color: var(--text-secondary);
  background: linear-gradient(135deg, #667eea10, #764ba210);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-medium);
}

.content-body :deep(code) {
  background: linear-gradient(135deg, #667eea15, #764ba215);
  padding: 2px 6px;
  border-radius: var(--radius-small);
  font-family: monospace;
}

.content-body :deep(pre) {
  background: linear-gradient(135deg, #667eea15, #764ba215);
  padding: var(--spacing-lg);
  border-radius: var(--radius-medium);
  overflow-x: auto;
  margin: var(--spacing-lg) 0;
}

/* Article navigation */
.article-navigation {
  display: flex;
  gap: var(--spacing-md);
  margin: var(--spacing-xl) 0;
}

.nav-button {
  flex: 1;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background: linear-gradient(135deg, #667eea15, #764ba215);
  border-color: transparent;
}

.nav-button.prev {
  justify-content: flex-start;
}

.nav-button.next {
  justify-content: flex-end;
}

.nav-spacer {
  width: var(--spacing-md);
}

/* Related articles */
.related-articles {
  margin-top: var(--spacing-2xl);
  padding-top: var(--spacing-xl);
  border-top: 1px solid rgba(255, 255, 255, 0.3);
}

.related-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
}

.related-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.related-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background: linear-gradient(135deg, #667eea10, #764ba210);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-medium);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.related-item:hover {
  background: linear-gradient(135deg, #667eea20, #764ba220);
  transform: translateY(-2px);
}

.related-article-title {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Article not found */
.not-found {
  max-width: 500px;
  margin: 100px auto;
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: var(--backdrop-blur-lg);
  padding: var(--spacing-3xl);
  border-radius: var(--radius-large);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-medium);
  position: relative;
  z-index: 1;
}

.not-found h3 {
  margin: var(--spacing-xl) 0 var(--spacing-md);
  font-size: var(--font-size-xl);
  color: var(--text-primary);
}

.not-found p {
  margin-bottom: var(--spacing-xl);
  color: var(--text-secondary);
}

/* Responsive design - Desktop */
@media (min-width: 769px) {
  .article-detail-page {
    padding: var(--spacing-3xl);
  }
  
  .article-container {
    padding: var(--spacing-3xl);
  }
  
  .article-title {
    font-size: var(--font-size-3xl);
  }
}

/* Responsive design - Tablet */
@media (max-width: 768px) and (min-width: 481px) {
  .article-detail-page {
    padding: var(--spacing-xl);
  }
  
  .article-container {
    padding: var(--spacing-xl);
  }
  
  .article-title {
    font-size: var(--font-size-2xl);
  }
  
  .article-navigation {
    flex-direction: column;
  }
}

/* Responsive design - Mobile */
@media (max-width: 480px) {
  .article-detail-page {
    padding: var(--spacing-md);
  }
  
  .article-container {
    padding: var(--spacing-lg);
  }
  
  .article-title {
    font-size: var(--font-size-xl);
  }
  
  .article-meta {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .article-navigation {
    flex-direction: column;
  }
}
</style>