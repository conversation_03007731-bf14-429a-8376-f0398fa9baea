<template>


		<!-- 时间显示区域 -->
		<div class="time">
			<span id="span_dt_dt" style="font-size: 1.8rem ;line-height:5rem">这是我们一起走过的</span>
			<div>
				<b id="tian">{{ loveTime.days }}天</b>
				<b id="shi">{{ liveTime.hours }}时</b>
				<b id="fen">{{ liveTime.minutes }}分</b>
				<b id="miao">{{ liveTime.seconds }}秒</b>
			</div>
		</div>

		<!-- 卡片导航区域 -->
		<div class="navigation-cards">
			<div class="cards-container">
				<el-row :gutter="24" justify="center">
					<el-col v-for="(card, index) in allCards" :key="card.name" :xs="24" :sm="12" :md="8" :lg="8" :xl="8" class="card-col">
						<el-card
							class="navigation-card"
							:class="{ 'animated fadeInUp': siteStore.config.animation === '1' }"
							shadow="hover"
							@click="navigateTo(card.path)"
						>
							<div class="card-content">
								<div class="card-icon">
									<img :src="card.icon" :alt="card.name" :style="card.name === '点点滴滴' ? { width: '55px !important' } : {}" />
								</div>
								<div class="card-info">
									<h3 class="card-title">{{ card.name }}</h3>
									<p class="card-description">{{ card.description }}</p>
								</div>
							</div>
						</el-card>
					</el-col>
				</el-row>
			</div>
		</div>

</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { useSiteStore } from '../stores/site';

const router = useRouter();
const siteStore = useSiteStore();

// 实时时间
const liveTime = ref({
	hours: '00',
	minutes: '00',
	seconds: '00',
});

// 恋爱天数
const loveTime = computed(() => siteStore.loveTime);



// 所有导航卡片
const allCards = ref([
	{
		name: '点点滴滴',
		description: '记录生活中的美好时光',
		icon: '/images/diandi.svg',
		path: '/little',
	},
	{
		name: '留言板',
		description: '留下你的祝福和话语',
		icon: '/images/comment.svg',
		path: '/leaving',
	},
	{
		name: '关于我们',
		description: '了解我们的故事',
		icon: '/images/yellow_heart.svg',
		path: '/about',
	},
	{
		name: 'Love Photo',
		description: '恋爱相册 记录最美瞬间',
		icon: '/images/gallary.svg',
		path: '/love-img',
	},
	{
		name: 'Love List',
		description: '恋爱列表 你我之间的约定',
		icon: '/images/red_menu.svg',
		path: '/love-list',
	},
]);

// 定时器
let timer = null;

// 更新实时时间
const updateLiveTime = () => {
	const now = new Date();
	liveTime.value = {
		hours: now.getHours().toString().padStart(2, '0'),
		minutes: now.getMinutes().toString().padStart(2, '0'),
		seconds: now.getSeconds().toString().padStart(2, '0'),
	};
};

// 导航跳转
const navigateTo = (path) => {
	router.push(path);
};

onMounted(() => {
	updateLiveTime();
	timer = setInterval(updateLiveTime, 1000);
});

onUnmounted(() => {
	if (timer) {
		clearInterval(timer);
	}
});
</script>

<style scoped>


/* 卡片导航区域样式 */
.navigation-cards {
	padding: 80px 10px;
	background: transparent;
}

.cards-container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 20px;
}

.card-col {
	margin-bottom: 32px;
}

.navigation-card {
	height: 150px;
	cursor: pointer;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	border-radius: 20px;
	border: 1px solid rgba(208, 206, 206, 0.4);
	background: #fff;
	box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
	overflow: hidden;
}

.navigation-card:hover {
	transform: translateY(-12px);
	box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
	border-color: rgba(74, 144, 226, 0.3);
}

:deep(.navigation-card .el-card__body) {
	padding: 0;
	height: 100%;
}

.card-content {
	display: flex;
	align-items: center;
	height: 100%;
	padding: 32px 24px;
	text-align: center;
}

.card-icon {
	margin-right: 1.8rem;
	transition: transform 0.3s ease;
}

.navigation-card:hover .card-icon {
	transform: scale(1.1);
}

.card-icon img {
	width: 72px;
	height: 72px;
	object-fit: contain;
	border-radius: 16px;
	filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.card-info {
	display: flex;
	flex-direction: column;
	justify-content: center;
}

.card-title {
	font-size: 1.8rem;
	font-weight: 700;
	color: #8d8888;
	margin-bottom: 0.5rem;
	line-height: 3rem;
	letter-spacing: 0.2rem;
	font-family:
		'Noto Serif SC',
		-apple-system,
		BlinkMacSystemFont,
		'Segoe UI',
		Roboto,
		sans-serif;
}

.card-title:hover {
	color: #6bacc4;
}

.card-description {
	letter-spacing: 0.3rem;
	font-size: 1.1rem;
	color: #7f8c8d;
	margin: 0;
	font-family:
		'Noto Serif SC',
		-apple-system,
		BlinkMacSystemFont,
		'Segoe UI',
		Roboto,
		sans-serif;
	font-weight: 400;
}

.card-description::after {
	content: '…';
}

/* 动画效果 */
.animated.fadeInUp {
	animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translate3d(0, 40px, 0);
	}
	to {
		opacity: 1;
		transform: translate3d(0, 0, 0);
	}
}

/* 响应式设计 */
@media (max-width: 992px) {
	.navigation-cards {
		padding: 60px 0;
	}

	.navigation-card {
		height: 200px;
	}

	.card-content {
		padding: 24px 20px;
	}

	.card-icon img {
		width: 60px;
		height: 60px;
	}

	.card-title {
		font-size: 1.8rem;
	}

	.card-description {
		font-size: 1.1rem;
	}
}

@media (max-width: 768px) {
	.navigation-cards {
		padding: 40px 0;
	}

	.cards-container {
		padding: 0 16px;
	}

	.card-col {
		margin-bottom: 24px;
	}

	.navigation-card {
		height: 180px;
	}

	.card-content {
		padding: 20px 16px;
	}

	.card-icon {
		margin-bottom: 16px;
	}

	.card-icon img {
		width: 52px;
		height: 52px;
	}

	.card-title {
		font-size: 1.8rem;
	}

	.card-description {
		font-size: 1.1rem;
	}
}

@media (max-width: 480px) {
	.navigation-cards {
		padding: 32px 0;
	}

	.card-col {
		margin-bottom: 20px;
	}

	.navigation-card {
		height: 160px;
	}

	.card-content {
		padding: 16px 12px;
	}

	.card-icon {
		margin-bottom: 12px;
	}

	.card-icon img {
		width: 44px;
		height: 44px;
	}

	.card-title {
		font-size: 1.8rem;
		margin-bottom: 8px;
	}

	.card-description {
		font-size: 1.1rem;
	}
}
</style>
