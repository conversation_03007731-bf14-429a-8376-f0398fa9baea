<template>
	<div class="little-page">
		<div class="container">
			<!-- 页面标题 -->
			<div class="page-header">
				<h1 class="page-title">我们的点点滴滴</h1>
				<p class="page-subtitle">记录两个人的甜蜜时光 💕</p>
			</div>

			<!-- 搜索和筛选 -->
			<div class="filter-section">
				<el-input 
					v-model="searchKeyword" 
					placeholder="搜索文章..." 
					:prefix-icon="Search" 
					clearable 
					@input="handleSearch" 
					class="search-input" 
				/>

				<div class="filter-controls">
					<el-select v-model="selectedCategory" placeholder="选择分类" clearable @change="handleFilterChange">
						<el-option v-for="category in categories" :key="category.value" :label="category.label" :value="category.value" />
					</el-select>

					<el-select v-model="sortBy" @change="handleFilterChange">
						<el-option label="最新发布" value="created_at-desc" />
						<el-option label="最早发布" value="created_at-asc" />
						<el-option label="标题排序" value="title-asc" />
					</el-select>
				</div>
			</div>

			<!-- 加载状态 -->
			<div v-if="loading" class="loading-container">
				<el-skeleton :rows="3" animated />
			</div>

			<!-- 文章列表 -->
			<div v-else-if="filteredArticles.length > 0" class="articles-container">
				<div class="articles-grid">
					<ArticleCard 
						v-for="(article, index) in paginatedArticles" 
						:key="article.id" 
						:article="article" 
						:index="index"
						:animate="true"
						@click="viewArticle" 
					/>
				</div>
			</div>

			<!-- 空状态 -->
			<div v-else class="empty-state">
				<el-empty description="暂无文章" />
			</div>

			<!-- 分页 -->
			<div v-if="filteredArticles.length > 0" class="pagination-container">
				<el-pagination
					v-model:current-page="currentPage"
					:page-size="pageSize"
					:total="filteredArticles.length"
					layout="prev, pager, next"
					@current-change="handlePageChange"
				/>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { Search } from '@element-plus/icons-vue';
import ArticleCard from '../components/ArticleCard.vue';
import { getArticles } from '../api/index.js';
import dayjs from 'dayjs';

const router = useRouter();

// Mock数据
const mockArticles = ref([
  {
    id: 1,
    title: '春日里的第一次约会',
    content: '今天和她一起去了公园，看着樱花飞舞，感受着春天的气息。她穿着淡粉色的裙子，笑容如花般灿烂。我们在小径上慢慢走着，聊着彼此的梦想和未来。那一刻，我觉得时间都静止了，只有她和我，还有满树的樱花见证着这美好的时光。',
    author: '毛双欢',
    category: 'sweet-moment',
    tags: ['约会', '春天', '樱花', '美好'],
    created_at: dayjs().subtract(2, 'day').toISOString(),
  },
  {
    id: 2,
    title: '第一次为她做早餐',
    content: '今天早上特地早起，想给她一个惊喜。虽然厨艺不精，但还是认真地煎了鸡蛋，烤了面包，还准备了她最爱的草莓酸奶。看到她睡眼惺忪地走出房间，然后惊喜地看着桌上的早餐，那个笑容真的让我觉得一切都是值得的。她说这是她吃过最美味的早餐。',
    author: '张家伟',
    category: 'daily-life',
    tags: ['早餐', '惊喜', '爱心', '美好'],
    created_at: dayjs().subtract(5, 'day').toISOString(),
  },
  {
    id: 3,
    title: '周末的咖啡时光',
    content: '周末的下午，阳光透过咖啡厅的落地窗洒在桌子上，点了一杯拿铁和她最爱的提拉米苏。我们坐在靠窗的位置，看着窗外匆忙的行人，聊着这一周发生的趣事。咖啡的香气弥漫在空气中，时光在这一刻变得格外温柔。',
    author: '毛双欢',
    category: 'sweet-moment',
    tags: ['咖啡', '周末', '甜品', '温柔'],
    created_at: dayjs().subtract(1, 'week').toISOString(),
  },
  {
    id: 4,
    title: '第一次一起做意大利面',
    content: '今天我们决定一起做晚餐，选择了意大利面。虽然两个人都是厨房新手，但一起摸索的过程特别有趣。她负责煮面条，我来调制酱汁，虽然厨房变得有点乱，但看着她认真的样子，心里满满的都是幸福。最后的成果虽然卖相一般，但味道意外地不错！',
    author: '张家伟',
    category: 'food-story',
    tags: ['意大利面', '一起做饭', '幸福', '回忆'],
    created_at: dayjs().subtract(10, 'day').toISOString(),
  },
  {
    id: 5,
    title: '我们的恋爱一周年',
    content: '今天是我们在一起的一周年纪念日，时间过得真快。还记得当初第一次见面时的紧张和兴奋，现在的我们已经能够很自然地在一起，分享彼此的快乐和烦恼。晚上我们去了第一次约会的那家餐厅，点了同样的菜，回忆起这一年来的点点滴滴。感恩能够遇见你，期待我们更美好的未来。',
    author: '毛双欢',
    category: 'anniversary',
    tags: ['纪念日', '一周年', '回忆', '感恩'],
    created_at: dayjs().subtract(3, 'day').toISOString(),
  },
  {
    id: 6,
    title: '雨天里的小确幸',
    content: '今天突然下起了大雨，我们没有带伞，只好在便利店的屋檐下避雨。看着雨滴打在地面上溅起的小水花，听着雨声的节拍，突然觉得这样的时光也很美好。她说，和你在一起，连下雨天都变得浪漫了。那一刻，我觉得自己是世界上最幸福的人。',
    author: '张家伟',
    category: 'sweet-moment',
    tags: ['雨天', '浪漫', '小确幸', '幸福'],
    created_at: dayjs().subtract(2, 'week').toISOString(),
  },
  {
    id: 7,
    title: '计划我们的第一次旅行',
    content: '最近我们开始计划第一次一起出去旅行，目标是去海边看日出。查攻略、订酒店、规划路线，每一个环节都让我们充满期待。虽然还在准备阶段，但光是想象我们一起看海、一起在沙滩上漫步的场景，就让人兴奋不已。希望这次旅行能成为我们美好的回忆。',
    author: '毛双欢',
    category: 'future-plan',
    tags: ['旅行计划', '海边', '日出', '期待'],
    created_at: dayjs().subtract(4, 'day').toISOString(),
  },
  {
    id: 8,
    title: '第一次一起看电影',
    content: '今晚我们一起去电影院看了一部爱情喜剧，她笑得很开心，我也被她的笑声感染了。电影散场后，我们在商场里逛了很久，买了一些小零食，然后坐在楼下的长椅上聊天到很晚。这样简单的快乐，让我觉得很幸福。有你在身边，平凡的日子也变得特别有意义。',
    author: '张家伟',
    category: 'sweet-moment',
    tags: ['电影', '约会', '快乐', '陪伴'],
    created_at: dayjs().subtract(6, 'day').toISOString(),
  }
]);

// 响应式数据
const loading = ref(true);
const searchKeyword = ref('');
const selectedCategory = ref('');
const sortBy = ref('created_at-desc');
const currentPage = ref(1);
const pageSize = ref(6);
const articles = ref([]);

// 分类选项
const categories = ref([
	{ label: '甜蜜时光', value: 'sweet-moment' },
	{ label: '日常生活', value: 'daily-life' },
	{ label: '一起旅行', value: 'travel-together' },
	{ label: '美食故事', value: 'food-story' },
	{ label: '纪念日', value: 'anniversary' },
	{ label: '未来计划', value: 'future-plan' },
]);

// 搜索防抖
let searchTimer = null;
const handleSearch = () => {
	clearTimeout(searchTimer);
	searchTimer = setTimeout(() => {
		currentPage.value = 1;
	}, 500);
};

// 筛选后的文章
const filteredArticles = computed(() => {
  let filtered = [...articles.value];
  
  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter(article => 
      article.title.toLowerCase().includes(keyword) ||
      article.content.toLowerCase().includes(keyword) ||
      (article.tags && article.tags.some(tag => tag.toLowerCase().includes(keyword)))
    );
  }
  
  // 按分类筛选
  if (selectedCategory.value) {
    filtered = filtered.filter(article => article.category === selectedCategory.value);
  }
  
  // 排序
  const [sortField, sortOrder] = sortBy.value.split('-');
  filtered.sort((a, b) => {
    let aVal, bVal;
    if (sortField === 'created_at') {
      aVal = new Date(a.created_at);
      bVal = new Date(b.created_at);
    } else if (sortField === 'title') {
      aVal = a.title;
      bVal = b.title;
    }
    
    if (sortOrder === 'desc') {
      return bVal > aVal ? 1 : -1;
    } else {
      return aVal > bVal ? 1 : -1;
    }
  });
  
  return filtered;
});

// 分页后的文章
const paginatedArticles = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredArticles.value.slice(start, end);
});

// 筛选变化处理
const handleFilterChange = () => {
  currentPage.value = 1;
};

// 页面变化处理
const handlePageChange = (newPage) => {
	currentPage.value = newPage;
	window.scrollTo({ top: 0, behavior: 'smooth' });
};

// 查看文章详情
const viewArticle = (id) => {
	router.push(`/article/${id}`);
};

// 获取文章数据
const fetchArticles = async () => {
	try {
		loading.value = true;
		const response = await getArticles();
		
		// 检查响应状态
		if (response && response.code === 200 && response.data) {
			articles.value = response.data;
		} else {
			// API 失败，使用 Mock 数据
			console.warn('API 响应异常，使用 Mock 数据');
			articles.value = mockArticles.value;
		}
	} catch (error) {
		// API 请求失败，使用 Mock 数据
		console.warn('API 请求失败，使用 Mock 数据:', error);
		articles.value = mockArticles.value;
	} finally {
		loading.value = false;
	}
};

// 生命周期
onMounted(() => {
	fetchArticles();
});
</script>

<style scoped>
.little-page {
	min-height: 100vh;
	padding: var(--spacing-2xl) 0;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	position: relative;
}

.little-page::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-image: linear-gradient(to right, rgba(255, 255, 255, 0.1) 1px, transparent 1px), 
					  linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
	background-size: 1.5rem 1.5rem;
	opacity: 0.3;
	pointer-events: none;
}

.container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 var(--spacing-lg);
}

/* 页面标题 */
.page-header {
	text-align: center;
	margin-bottom: var(--spacing-3xl);
}

.page-title {
	color: var(--text-white);
	font-size: var(--font-size-responsive-4xl);
	font-weight: var(--font-weight-bold);
	margin-bottom: var(--spacing-responsive-md);
	text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
	background: linear-gradient(270deg, #ffffff, #f0f8ff, #ffffff);
	background-size: 200% 200%;
	-webkit-background-clip: text;
	background-clip: text;
	animation: gradient-shift 3s ease-in-out infinite;
	line-height: var(--line-height-tight);
}

.page-subtitle {
	color: rgba(255, 255, 255, 0.9);
	font-size: var(--font-size-responsive-lg);
	font-weight: var(--font-weight-normal);
	margin: 0;
	line-height: var(--line-height-normal);
}

/* 筛选区域 */
.filter-section {
	background: var(--bg-glass);
	backdrop-filter: var(--backdrop-blur-lg);
	border: 1px solid var(--border-light);
	border-radius: var(--radius-large);
	padding: var(--spacing-lg);
	margin-bottom: var(--spacing-2xl);
	display: flex;
	flex-direction: column;
	gap: var(--spacing-md);
}

.search-input {
	max-width: 500px;
	margin: 0 auto;
}

.filter-controls {
	display: flex;
	justify-content: center;
	gap: var(--spacing-md);
	flex-wrap: wrap;
}

/* 加载状态 */
.loading-container {
	background: var(--bg-glass);
	backdrop-filter: var(--backdrop-blur-lg);
	border: 1px solid var(--border-light);
	border-radius: var(--radius-large);
	padding: var(--spacing-2xl);
}

/* 文章列表 */
.articles-container {
	margin-bottom: var(--spacing-2xl);
}

.articles-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
	gap: var(--spacing-xl);
	margin-bottom: var(--spacing-2xl);
}

/* 空状态 */
.empty-state {
	text-align: center;
	padding: var(--spacing-3xl) var(--spacing-lg);
	background: var(--bg-glass);
	backdrop-filter: var(--backdrop-blur-lg);
	border: 1px solid var(--border-light);
	border-radius: var(--radius-large);
	color: var(--text-white);
}

.pagination-container {
	display: flex;
	justify-content: center;
	padding: var(--spacing-xl) 0;
	background: var(--bg-glass);
	backdrop-filter: var(--backdrop-blur-lg);
	border: 1px solid var(--border-light);
	border-radius: var(--radius-large);
}

.pagination-container :deep(.el-pager li) {
	background: rgba(255, 255, 255, 0.1);
	color: rgba(255, 255, 255, 0.9);
	border-radius: var(--radius-small);
	margin: 0 2px;
	transition: all var(--transition-normal);
}

.pagination-container :deep(.el-pager li:hover),
.pagination-container :deep(.el-pager li.is-active) {
	background: rgba(255, 255, 255, 0.2);
	color: white;
}

.pagination-container :deep(.btn-prev),
.pagination-container :deep(.btn-next) {
	background: rgba(255, 255, 255, 0.1);
	color: rgba(255, 255, 255, 0.9);
	border-radius: var(--radius-small);
}

.pagination-container :deep(.btn-prev:hover),
.pagination-container :deep(.btn-next:hover) {
	background: rgba(255, 255, 255, 0.2);
	color: white;
}

/* 响应式设计 */
@media (max-width: 1024px) {
	.articles-grid {
		grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
		gap: var(--spacing-lg);
	}

	.filter-section {
		padding: var(--spacing-responsive-md);
	}
}

@media (max-width: 768px) {
	.little-page {
		padding: var(--spacing-responsive-xl) 0;
	}

	.container {
		padding: 0 var(--spacing-md);
	}

	.page-title {
		font-size: var(--font-size-responsive-3xl);
	}

	.page-subtitle {
		font-size: var(--font-size-responsive-base);
	}

	.filter-section {
		padding: var(--spacing-responsive-sm);
		margin-bottom: var(--spacing-responsive-xl);
	}

	.filter-controls {
		flex-direction: column;
		align-items: stretch;
		gap: var(--spacing-responsive-sm);
	}

	.search-input {
		max-width: 100%;
	}

	.articles-grid {
		grid-template-columns: 1fr;
		gap: var(--spacing-md);
	}

	.pagination-container {
		padding: var(--spacing-responsive-sm);
	}
}

@media (max-width: 480px) {
	.page-title {
		font-size: var(--font-size-responsive-2xl);
		margin-bottom: var(--spacing-responsive-sm);
	}

	.page-subtitle {
		font-size: var(--font-size-responsive-sm);
	}

	.filter-section {
		margin-bottom: var(--spacing-responsive-lg);
		padding: var(--spacing-responsive-sm);
	}

	.empty-state {
		padding: var(--spacing-responsive-lg) var(--spacing-responsive-sm);
	}
}

/* 横屏适配 */
@media screen and (orientation: landscape) and (max-height: 600px) {
	.little-page {
		padding: var(--spacing-responsive-md) 0;
	}

	.page-header {
		margin-bottom: var(--spacing-responsive-lg);
	}

	.page-title {
		font-size: var(--font-size-responsive-2xl);
		margin-bottom: var(--spacing-responsive-xs);
	}

	.page-subtitle {
		font-size: var(--font-size-responsive-sm);
	}

	.filter-section {
		padding: var(--spacing-responsive-sm);
		margin-bottom: var(--spacing-responsive-lg);
	}

	.filter-controls {
		flex-direction: row;
		flex-wrap: wrap;
		justify-content: center;
		gap: var(--spacing-responsive-xs);
	}
}

/* 竖屏适配 */
@media screen and (orientation: portrait) {
	.page-header {
		margin-bottom: var(--spacing-responsive-xl);
	}

	.filter-section {
		margin-bottom: var(--spacing-responsive-xl);
	}
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
	.pagination-container :deep(.el-pager li) {
		min-height: var(--touch-target-min);
		min-width: var(--touch-target-min);
		display: flex;
		align-items: center;
		justify-content: center;
	}
}

/* 动画效果 */
@keyframes gradient-shift {
	0%,
	100% {
		background-position: 0% 50%;
	}

	50% {
		background-position: 100% 50%;
	}
}

/* 打印样式 */
@media print {
	.little-page {
		background: white;
		color: black;
	}

	.filter-section,
	.pagination-container {
		display: none;
	}

	.articles-grid {
		grid-template-columns: 1fr;
	}
}
</style>
