<template>
  <div id="app">
		<Header/>
    <RouterView />
    <Footer />

  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useSiteStore } from './stores/site'
import Footer from './components/Footer.vue'
import Header from '@/components/Header.vue';
const isDev = import.meta.env.DEV

const siteStore = useSiteStore()

onMounted(() => {
  // 初始化网站配置
  siteStore.fetchSiteConfig()
})
</script>

@import url('https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;700&display=swap');

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

ul {
  list-style: none;
}

a {
  text-decoration: none;
  transition: all .15s;
}

a:hover {
  color: #10bbff;
}

:root {
  font-size: 14px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: 1rem;
}

input,
textarea {
  outline: none;
}

body {
  background-image: linear-gradient(to right, rgba(37, 82, 110, 0.1) 1px, transparent 1px), linear-gradient(to bottom, rgba(37, 82, 110, 0.1) 1px, transparent 1px);
  background-size: 1.5rem 1.5rem;
  font-family: 'Noto Serif SC', serif;
  font-weight: 400;
  min-height: 100vh;
}

#app {
  min-height: 100vh;
  position: relative;
}


/* 布局容器 */
.central {
  width: 1180px;
  margin: 2rem auto;
  padding: 1rem;
  box-sizing: border-box;
}

.central-800 {
  width: 800px;
}

.central-600 {
  width: 600px;
}
/* 背景图片区域 */
.bg-img {
  width: 100%;
  box-sizing: border-box;
  background: url('/images/headCover.jpg') no-repeat center;
  background-size: cover;
  padding-top: 8rem;
  position: relative;
}

.bg-img .middle {
  display: flex;
  align-items: center;
  justify-content: space-around;
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  background: #ffffff1f;
  padding: 3rem 5rem 2rem;
  border-radius: 4rem;
  user-select: none;
}

.bg-img .middle img {
  width: 10rem;
  height: 10rem;
  border-radius: 10rem;
  border: 0.2rem solid #fff;
}

.bg-img .middle .img-male span,
.bg-img .middle .img-female span {
  display: block;
  text-align: center;
  font-size: 1.5rem;
  margin-top: 1rem;
  color: #fff;
  font-family: 'Noto Serif SC', serif;
  font-weight: 700;
}

.bg-img .middle .love-icon img {
  width: 7rem;
  height: 7rem;
  border: none;
  animation: love-pulse 2s linear infinite;
}

@keyframes love-pulse {
  0% {
    transform: scale(0.8, 0.8);
  }
  70% {
    transform: scale(1.3, 1.3);
  }
  100% {
    transform: scale(0.8, 0.8);
  }
}

/* 时间显示 */
.time {
  text-align: center;
  color: #1a1a1a;
  margin: 3rem 0 2rem;
}

.time span {
  background: linear-gradient(270deg, #986fee, #8695e6, #68b7dd, #18d7d3);
  font-size: 1.4rem;
  background-size: 2000rem;
  -webkit-background-clip: text;
  color: #0000;
  animation: rainbow 60s linear infinite;
  font-family: 'Noto Serif SC', serif;
  font-weight: 700;
}

.time b {
  font-size: 2.7rem;
  font-family: 'Noto Serif SC', serif;
  font-weight: 700;
}

@keyframes rainbow {
  to {
    background-position: -2000rem;
  }
}

/* 卡片样式 */
.card-wrap .row .card {
  padding: 2rem;
  border-radius: 1.5rem;
  background: #fff;
  box-shadow: 0 8px 12px #ebedf0;
  border: 1px solid rgba(208, 206, 206, 0.4) !important;
  transition: all 0.08s linear;
  cursor: pointer;
}

.card-wrap .row .card:hover {
  box-shadow: 0 6px 10px #e9e9e9;
  transform: translateY(-6px);
  transition: all 0.1s linear;
}

.card-wrap .row .flex-h {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.card-wrap .card-b {
  margin: 0 auto;
  width: 80%;
  padding: 1.5rem;
  border-radius: 2rem;
  background: #fff;
  box-shadow: 0 8px 12px #ebedf0;
  border: 1px solid rgba(208, 206, 206, 0.4) !important;
  font-family: 'Noto Serif SC', serif;
  font-weight: 700;
}

.card-wrap .card-b img {
  width: 8rem;
  margin-right: 5rem;
}

.card-wrap .card-b .text {
  flex-grow: 2;
}

.card-wrap .card-b .text span {
  font-size: 2rem;
  line-height: 5rem;
}

.card-wrap .card-b .text p {
  font-size: 1.2rem;
  color: #959595;
  font-weight: 500;
}

/* 波浪效果 */
.bg-img .waves {
  position: relative;
  width: 100%;
  height: 5rem;
  margin-top: 5rem;
  margin-bottom: -7px;
}

.bg-img .parallax > use {
  animation: move-forever 25s cubic-bezier(0.55, 0.5, 0.45, 0.5) infinite;
}

.bg-img .parallax > use:nth-child(1) {
  animation-delay: -2s;
  animation-duration: 7s;
}

.bg-img .parallax > use:nth-child(2) {
  animation-delay: -3s;
  animation-duration: 10s;
}

.bg-img .parallax > use:nth-child(3) {
  animation-delay: -4s;
  animation-duration: 13s;
}

.bg-img .parallax > use:nth-child(4) {
  animation-delay: -5s;
  animation-duration: 20s;
}

@keyframes move-forever {
  0% {
    transform: translate3d(-90px, 0, 0);
  }
  100% {
    transform: translate3d(85px, 0, 0);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .central {
    width: 930px;
  }
}

@media (max-width: 960px) {
  .central {
    width: 728px;
  }
}

@media (max-width: 768px) {
  .central {
    width: 100%;
    padding: 1rem;
  }
  
  .row {
    gap: 1rem;
  }
  
  .col-lg-4,
  .col-lg-6,
  .col-lg-12,
  .col-md-6,
  .col-md-12,
  .col-sm-12 {
    grid-column: span 12;
  }
  
  .bg-img .middle {
    padding: 2rem 2rem 1rem;
    flex-direction: column;
    gap: 1rem;
  }
  
  .bg-img .middle img {
    width: 6rem;
    height: 6rem;
  }
  
  .bg-img .middle .love-icon img {
    width: 4rem;
    height: 4rem;
  }
  
  .card-wrap .card-b {
    width: 100%;
  }
  
  .card-wrap .card-b img {
    width: 6rem;
    margin-right: 2rem;
  }
}

/* 动画类 */
.animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}

.fadeInUp {
  animation-name: fadeInUp;
}

.fadeInDown {
  animation-name: fadeInDown;
}

.delay-03s {
  animation-delay: .3s;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 100%, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translate3d(0, -100%, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
</style>
