/* 统一的样式变量 */
@import './responsive.css';
@import './touch.css';

:root {
  /* 主色调 */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --accent-gradient: linear-gradient(270deg, #986fee, #8695e6, #68b7dd, #18d7d3);
  
  /* 背景色 */
  --bg-primary: rgba(255, 255, 255, 0.95);
  --bg-secondary: rgba(255, 255, 255, 0.8);
  --bg-overlay: rgba(0, 0, 0, 0.1);
  --bg-glass: rgba(255, 255, 255, 0.1);
  --bg-card: #ffffff;
  
  /* 文字色 */
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-muted: #999999;
  --text-white: #ffffff;
  --text-gradient: linear-gradient(270deg, #986fee, #8695e6, #68b7dd, #18d7d3);
  
  /* 边框色 */
  --border-light: rgba(255, 255, 255, 0.2);
  --border-primary: #e0e0e0;
  --border-secondary: rgba(208, 206, 206, 0.4);
  
  /* 阴影 */
  --shadow-light: 0 5px 15px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 10px 30px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 20px 40px rgba(0, 0, 0, 0.2);
  --shadow-card: 0 8px 12px #ebedf0;
  --shadow-card-hover: 0 6px 10px #e9e9e9;
  
  /* 圆角 */
  --radius-small: 8px;
  --radius-medium: 12px;
  --radius-large: 15px;
  --radius-xl: 20px;
  --radius-round: 50%;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  --spacing-3xl: 64px;
  
  /* 字体大小 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 30px;
  --font-size-4xl: 36px;
  
  /* 字体权重 */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* 行高 */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* 过渡动画 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  --transition-bounce: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Z-index层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  
  /* 响应式断点 */
  --breakpoint-xs: 480px;
  --breakpoint-sm: 768px;
  --breakpoint-md: 1024px;
  --breakpoint-lg: 1200px;
  --breakpoint-xl: 1440px;
  
  /* 容器宽度 */
  --container-xs: 100%;
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
  
  /* 毛玻璃效果 */
  --backdrop-blur-sm: blur(4px);
  --backdrop-blur-md: blur(8px);
  --backdrop-blur-lg: blur(15px);
  --backdrop-blur-xl: blur(20px);
}

/* 深色模式变量 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: rgba(30, 30, 30, 0.95);
    --bg-secondary: rgba(40, 40, 40, 0.8);
    --bg-card: #2a2a2a;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #999999;
    --border-primary: #404040;
    --border-secondary: rgba(255, 255, 255, 0.1);
  }
}

/* 自定义属性类 */
.text-gradient {
  background: var(--text-gradient);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  animation: gradient-shift 3s ease-in-out infinite;
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.glass-effect {
  backdrop-filter: var(--backdrop-blur-lg);
  -webkit-backdrop-filter: var(--backdrop-blur-lg);
  background: var(--bg-glass);
  border: 1px solid var(--border-light);
}

.card-shadow {
  box-shadow: var(--shadow-card);
  transition: box-shadow var(--transition-normal);
}

.card-shadow:hover {
  box-shadow: var(--shadow-card-hover);
}