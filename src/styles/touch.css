/* 触摸交互优化样式 */

/* ===== 触摸设备检测和基础优化 ===== */
:root {
  /* 触摸友好的尺寸 */
  --touch-target-size: 44px;
  --touch-target-spacing: 8px;
  --touch-scroll-padding: 16px;
  
  /* 触摸反馈时间 */
  --touch-feedback-duration: 0.15s;
  --touch-ripple-duration: 0.6s;
  
  /* 触摸区域扩展 */
  --touch-area-extend: 4px;
}

/* ===== 触摸目标优化 ===== */
/* 基础触摸目标 */
.touch-target {
  min-height: var(--touch-target-size);
  min-width: var(--touch-target-size);
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

/* 扩展触摸区域 */
.touch-target::before {
  content: '';
  position: absolute;
  top: calc(-1 * var(--touch-area-extend));
  left: calc(-1 * var(--touch-area-extend));
  right: calc(-1 * var(--touch-area-extend));
  bottom: calc(-1 * var(--touch-area-extend));
  z-index: -1;
}

/* 触摸反馈效果 */
.touch-feedback {
  position: relative;
  overflow: hidden;
  transition: all var(--touch-feedback-duration) ease;
}

.touch-feedback:active {
  transform: scale(0.95);
  opacity: 0.8;
}

/* 波纹效果 */
.touch-ripple {
  position: relative;
  overflow: hidden;
}

.touch-ripple::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width var(--touch-ripple-duration) ease,
              height var(--touch-ripple-duration) ease;
}

.touch-ripple:active::after {
  width: 200px;
  height: 200px;
}

/* ===== 按钮触摸优化 ===== */
button,
.el-button,
[role="button"] {
  min-height: var(--touch-target-size);
  min-width: var(--touch-target-size);
  padding: max(var(--touch-target-spacing), 8px) max(var(--touch-target-spacing), 16px);
  border-radius: max(8px, var(--radius-medium));
  position: relative;
  overflow: hidden;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

/* 按钮触摸状态 */
button:active,
.el-button:active,
[role="button"]:active {
  transform: scale(0.98);
  transition: transform var(--touch-feedback-duration) ease;
}

/* 小按钮的触摸优化 */
.el-button--small {
  min-height: calc(var(--touch-target-size) - 8px);
  min-width: calc(var(--touch-target-size) - 8px);
  padding: max(6px, var(--touch-target-spacing)) max(12px, var(--touch-target-spacing));
}

/* 圆形按钮触摸优化 */
.el-button.is-circle {
  width: var(--touch-target-size);
  height: var(--touch-target-size);
  padding: 0;
}

/* ===== 表单元素触摸优化 ===== */
input,
textarea,
select,
.el-input__wrapper,
.el-textarea__inner,
.el-select__wrapper {
  min-height: var(--touch-target-size);
  padding: max(var(--touch-target-spacing), 8px) max(var(--touch-target-spacing), 12px);
  border-radius: var(--radius-medium);
  font-size: max(16px, var(--font-size-base)); /* 防止iOS自动缩放 */
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

/* 输入框焦点状态 */
input:focus,
textarea:focus,
.el-input__wrapper.is-focus,
.el-textarea__inner:focus {
  outline: 2px solid var(--el-color-primary);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(64, 158, 255, 0.2);
}

/* 复选框和单选框触摸优化 */
.el-checkbox,
.el-radio {
  min-height: var(--touch-target-size);
  padding: var(--touch-target-spacing);
  margin: var(--touch-target-spacing) 0;
}

.el-checkbox__input,
.el-radio__input {
  margin-right: var(--touch-target-spacing);
}

.el-checkbox__inner,
.el-radio__inner {
  width: 20px;
  height: 20px;
}

/* ===== 链接触摸优化 ===== */
a {
  min-height: var(--touch-target-size);
  display: inline-flex;
  align-items: center;
  padding: var(--touch-target-spacing);
  margin: calc(-1 * var(--touch-target-spacing));
  border-radius: var(--radius-small);
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

/* 文本链接特殊处理 */
a.text-link {
  min-height: auto;
  display: inline;
  padding: 2px 4px;
  margin: -2px -4px;
}

/* ===== 卡片和可点击区域优化 ===== */
.article-card,
.card-interactive,
[role="button"] {
  position: relative;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
  transition: transform var(--touch-feedback-duration) ease,
              box-shadow var(--touch-feedback-duration) ease;
}

.article-card:active,
.card-interactive:active {
  transform: scale(0.98);
}

/* ===== 滚动优化 ===== */
/* 平滑滚动 */
.smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* 滚动容器优化 */
.scroll-container {
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  scroll-padding: var(--touch-scroll-padding);
  overscroll-behavior: contain;
}

/* 水平滚动优化 */
.scroll-horizontal {
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  scroll-snap-type: x mandatory;
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
}

.scroll-horizontal > * {
  flex-shrink: 0;
  scroll-snap-align: start;
}

/* 垂直滚动优化 */
.scroll-vertical {
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  scroll-snap-type: y mandatory;
  max-height: 100vh;
}

.scroll-vertical > * {
  scroll-snap-align: start;
}

/* ===== 手势支持 ===== */
/* 滑动手势 */
.swipeable {
  touch-action: pan-x;
  position: relative;
  overflow: hidden;
}

/* 拖拽优化 */
.draggable {
  touch-action: none;
  user-select: none;
  -webkit-user-drag: element;
  cursor: grab;
}

.draggable:active {
  cursor: grabbing;
}

/* 缩放手势 */
.pinch-zoom {
  touch-action: pinch-zoom;
}

/* ===== 导航触摸优化 ===== */
.nav-item,
.menu-item {
  min-height: var(--touch-target-size);
  padding: var(--touch-target-spacing) var(--spacing-md);
  display: flex;
  align-items: center;
  border-radius: var(--radius-medium);
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
  transition: background-color var(--touch-feedback-duration) ease;
}

.nav-item:active,
.menu-item:active {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 标签页触摸优化 */
.el-tabs__item {
  min-height: var(--touch-target-size);
  padding: var(--touch-target-spacing) var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ===== 模态框和弹出层触摸优化 ===== */
.el-dialog,
.el-drawer,
.el-popover {
  touch-action: manipulation;
}

/* 关闭按钮优化 */
.el-dialog__close,
.el-drawer__close-btn,
.el-message-box__close {
  min-width: var(--touch-target-size);
  min-height: var(--touch-target-size);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ===== 触摸设备特定样式 ===== */
@media (hover: none) and (pointer: coarse) {
  /* 移除悬停效果 */
  .hover-effect:hover,
  .card-interactive:hover,
  .article-card:hover {
    transform: none;
    box-shadow: inherit;
  }
  
  /* 增强触摸反馈 */
  .touch-enhanced {
    transition: all var(--touch-feedback-duration) ease;
  }
  
  .touch-enhanced:active {
    transform: scale(0.95);
    opacity: 0.8;
  }
  
  /* 优化文本选择 */
  .selectable-text {
    user-select: text;
    -webkit-user-select: text;
  }
  
  /* 禁用文本选择的元素 */
  .no-select,
  button,
  .el-button,
  [role="button"] {
    user-select: none;
    -webkit-user-select: none;
  }
  
  /* 优化输入框 */
  input,
  textarea {
    font-size: 16px; /* 防止iOS缩放 */
  }
  
  /* 优化下拉菜单 */
  .el-select-dropdown,
  .el-dropdown-menu {
    max-height: 50vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .el-select-dropdown__item,
  .el-dropdown-menu__item {
    min-height: var(--touch-target-size);
    padding: var(--touch-target-spacing) var(--spacing-md);
    display: flex;
    align-items: center;
  }
}

/* ===== 触摸设备的特殊交互 ===== */
/* 长按效果 */
.long-press {
  position: relative;
}

.long-press::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  opacity: 0;
  border-radius: inherit;
  transition: opacity 0.3s ease;
}

.long-press.long-pressing::after {
  opacity: 1;
}

/* 双击效果 */
.double-tap {
  position: relative;
  overflow: hidden;
}

.double-tap::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.double-tap.double-tapped::before {
  width: 100px;
  height: 100px;
}

/* ===== 可访问性增强 ===== */
/* 焦点指示器 */
.focus-visible {
  outline: 2px solid var(--el-color-primary);
  outline-offset: 2px;
  border-radius: var(--radius-small);
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  button,
  .el-button,
  input,
  textarea,
  .el-input__wrapper {
    border: 2px solid currentColor;
  }
  
  .touch-feedback:active {
    background-color: rgba(0, 0, 0, 0.2);
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .touch-feedback,
  .touch-ripple,
  button,
  .el-button,
  .card-interactive {
    transition: none;
  }
  
  .touch-feedback:active,
  .card-interactive:active {
    transform: none;
  }
  
  .touch-ripple::after {
    display: none;
  }
}

/* ===== 设备特定优化 ===== */
/* iPhone X 系列安全区域 */
@supports (padding: max(0px)) {
  .safe-area-inset-top {
    padding-top: max(var(--spacing-md), env(safe-area-inset-top));
  }
  
  .safe-area-inset-bottom {
    padding-bottom: max(var(--spacing-md), env(safe-area-inset-bottom));
  }
  
  .safe-area-inset-left {
    padding-left: max(var(--spacing-md), env(safe-area-inset-left));
  }
  
  .safe-area-inset-right {
    padding-right: max(var(--spacing-md), env(safe-area-inset-right));
  }
}

/* Android 设备优化 */
@media screen and (-webkit-min-device-pixel-ratio: 1) {
  /* Android 特定的触摸优化 */
  .android-optimized {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  }
}

/* iOS 设备优化 */
@supports (-webkit-touch-callout: none) {
  /* iOS 特定的触摸优化 */
  .ios-optimized {
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
  }
  
  /* iOS 滚动优化 */
  .ios-scroll {
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
  }
}

/* ===== 调试和开发工具 ===== */
/* 触摸区域可视化（仅开发环境） */
.debug-touch .touch-target::before {
  background: rgba(255, 0, 0, 0.2);
  border: 1px dashed red;
}

.debug-touch button::before,
.debug-touch .el-button::before {
  background: rgba(0, 255, 0, 0.2);
  border: 1px dashed green;
}

/* 触摸事件日志 */
.debug-touch-events {
  position: fixed;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px;
  border-radius: 5px;
  font-family: monospace;
  font-size: 12px;
  z-index: 9999;
  max-width: 300px;
  max-height: 200px;
  overflow-y: auto;
}