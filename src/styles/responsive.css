/* 响应式设计系统 */

/* ===== 响应式断点系统 ===== */
:root {
  /* 断点定义 */
  --breakpoint-xs: 480px;   /* 超小屏幕 - 手机竖屏 */
  --breakpoint-sm: 768px;   /* 小屏幕 - 手机横屏/小平板 */
  --breakpoint-md: 1024px;  /* 中等屏幕 - 平板 */
  --breakpoint-lg: 1200px;  /* 大屏幕 - 桌面 */
  --breakpoint-xl: 1440px;  /* 超大屏幕 - 大桌面 */
  --breakpoint-2xl: 1920px; /* 超宽屏 */

  /* 容器最大宽度 */
  --container-xs: 100%;
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;

  /* 响应式间距 */
  --spacing-responsive-xs: clamp(8px, 2vw, 16px);
  --spacing-responsive-sm: clamp(12px, 3vw, 24px);
  --spacing-responsive-md: clamp(16px, 4vw, 32px);
  --spacing-responsive-lg: clamp(24px, 6vw, 48px);
  --spacing-responsive-xl: clamp(32px, 8vw, 64px);

  /* 响应式字体大小 */
  --font-size-responsive-xs: clamp(12px, 2.5vw, 14px);
  --font-size-responsive-sm: clamp(14px, 3vw, 16px);
  --font-size-responsive-base: clamp(16px, 3.5vw, 18px);
  --font-size-responsive-lg: clamp(18px, 4vw, 22px);
  --font-size-responsive-xl: clamp(20px, 4.5vw, 28px);
  --font-size-responsive-2xl: clamp(24px, 5vw, 36px);
  --font-size-responsive-3xl: clamp(30px, 6vw, 48px);
  --font-size-responsive-4xl: clamp(36px, 7vw, 60px);

  /* 触摸友好的最小尺寸 */
  --touch-target-min: 44px;
  --touch-spacing-min: 8px;
}

/* ===== 响应式容器系统 ===== */
.container {
  width: 100%;
  margin: 0 auto;
  padding-left: var(--spacing-responsive-sm);
  padding-right: var(--spacing-responsive-sm);
}

.container-xs { max-width: var(--container-xs); }
.container-sm { max-width: var(--container-sm); }
.container-md { max-width: var(--container-md); }
.container-lg { max-width: var(--container-lg); }
.container-xl { max-width: var(--container-xl); }
.container-2xl { max-width: var(--container-2xl); }

.container-fluid {
  width: 100%;
  padding-left: var(--spacing-responsive-sm);
  padding-right: var(--spacing-responsive-sm);
}

/* ===== 响应式网格系统 ===== */
.grid-responsive {
  display: grid;
  gap: var(--spacing-responsive-md);
}

/* 自适应网格 */
.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

/* 响应式网格列数 */
.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
.grid-cols-6 { grid-template-columns: repeat(6, 1fr); }

/* ===== 响应式文字系统 ===== */
.text-responsive-xs { font-size: var(--font-size-responsive-xs); }
.text-responsive-sm { font-size: var(--font-size-responsive-sm); }
.text-responsive-base { font-size: var(--font-size-responsive-base); }
.text-responsive-lg { font-size: var(--font-size-responsive-lg); }
.text-responsive-xl { font-size: var(--font-size-responsive-xl); }
.text-responsive-2xl { font-size: var(--font-size-responsive-2xl); }
.text-responsive-3xl { font-size: var(--font-size-responsive-3xl); }
.text-responsive-4xl { font-size: var(--font-size-responsive-4xl); }

/* 响应式行高 */
.leading-responsive-tight { line-height: clamp(1.2, 1.25, 1.3); }
.leading-responsive-normal { line-height: clamp(1.4, 1.5, 1.6); }
.leading-responsive-relaxed { line-height: clamp(1.6, 1.75, 1.8); }

/* ===== 响应式间距系统 ===== */
.spacing-responsive-xs { margin: var(--spacing-responsive-xs); }
.spacing-responsive-sm { margin: var(--spacing-responsive-sm); }
.spacing-responsive-md { margin: var(--spacing-responsive-md); }
.spacing-responsive-lg { margin: var(--spacing-responsive-lg); }
.spacing-responsive-xl { margin: var(--spacing-responsive-xl); }

.p-responsive-xs { padding: var(--spacing-responsive-xs); }
.p-responsive-sm { padding: var(--spacing-responsive-sm); }
.p-responsive-md { padding: var(--spacing-responsive-md); }
.p-responsive-lg { padding: var(--spacing-responsive-lg); }
.p-responsive-xl { padding: var(--spacing-responsive-xl); }

.m-responsive-xs { margin: var(--spacing-responsive-xs); }
.m-responsive-sm { margin: var(--spacing-responsive-sm); }
.m-responsive-md { margin: var(--spacing-responsive-md); }
.m-responsive-lg { margin: var(--spacing-responsive-lg); }
.m-responsive-xl { margin: var(--spacing-responsive-xl); }

/* ===== 触摸优化 ===== */
.touch-target {
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
  display: flex;
  align-items: center;
  justify-content: center;
}

.touch-friendly {
  padding: var(--touch-spacing-min);
  margin: var(--touch-spacing-min);
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  /* 移除悬停效果，优化触摸体验 */
  .hover-effect:hover {
    transform: none;
  }
  
  /* 增大可点击区域 */
  button, 
  .el-button,
  a,
  [role="button"] {
    min-height: var(--touch-target-min);
    min-width: var(--touch-target-min);
    padding: max(var(--touch-spacing-min), var(--spacing-sm));
  }
  
  /* 优化表单元素 */
  input,
  textarea,
  select,
  .el-input__wrapper,
  .el-textarea__inner {
    min-height: var(--touch-target-min);
    font-size: max(16px, var(--font-size-base)); /* 防止iOS缩放 */
  }
}

/* ===== 屏幕方向适配 ===== */
/* 横屏适配 */
@media screen and (orientation: landscape) {
  .landscape-hide { display: none; }
  .landscape-show { display: block; }
  
  /* 横屏时减少垂直间距 */
  .landscape-compact {
    padding-top: var(--spacing-sm);
    padding-bottom: var(--spacing-sm);
  }
  
  /* 横屏时的导航优化 */
  .landscape-nav {
    flex-direction: row;
    justify-content: space-between;
  }
}

/* 竖屏适配 */
@media screen and (orientation: portrait) {
  .portrait-hide { display: none; }
  .portrait-show { display: block; }
  
  /* 竖屏时增加垂直间距 */
  .portrait-spacious {
    padding-top: var(--spacing-lg);
    padding-bottom: var(--spacing-lg);
  }
  
  /* 竖屏时的导航优化 */
  .portrait-nav {
    flex-direction: column;
    align-items: center;
  }
}

/* ===== 设备特定优化 ===== */
/* 手机设备 */
@media screen and (max-width: 767px) {
  /* 移动端优化 */
  .mobile-hide { display: none !important; }
  .mobile-show { display: block !important; }
  .mobile-flex { display: flex !important; }
  
  /* 移动端布局 */
  .mobile-stack {
    flex-direction: column;
    align-items: stretch;
  }
  
  .mobile-center {
    text-align: center;
    align-items: center;
    justify-content: center;
  }
  
  /* 移动端间距 */
  .mobile-compact {
    padding: var(--spacing-sm);
    margin: var(--spacing-xs) 0;
  }
  
  /* 移动端字体 */
  .mobile-text-sm { font-size: var(--font-size-sm); }
  .mobile-text-base { font-size: var(--font-size-base); }
  .mobile-text-lg { font-size: var(--font-size-lg); }
  
  /* 移动端网格 */
  .mobile-grid-1 { grid-template-columns: 1fr; }
  .mobile-grid-2 { grid-template-columns: repeat(2, 1fr); }
  
  /* 移动端全宽 */
  .mobile-full-width {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
  }
}

/* 平板设备 */
@media screen and (min-width: 768px) and (max-width: 1023px) {
  .tablet-hide { display: none !important; }
  .tablet-show { display: block !important; }
  .tablet-flex { display: flex !important; }
  
  /* 平板端网格 */
  .tablet-grid-2 { grid-template-columns: repeat(2, 1fr); }
  .tablet-grid-3 { grid-template-columns: repeat(3, 1fr); }
  
  /* 平板端布局 */
  .tablet-row {
    flex-direction: row;
    flex-wrap: wrap;
  }
}

/* 桌面设备 */
@media screen and (min-width: 1024px) {
  .desktop-hide { display: none !important; }
  .desktop-show { display: block !important; }
  .desktop-flex { display: flex !important; }
  
  /* 桌面端网格 */
  .desktop-grid-3 { grid-template-columns: repeat(3, 1fr); }
  .desktop-grid-4 { grid-template-columns: repeat(4, 1fr); }
  .desktop-grid-5 { grid-template-columns: repeat(5, 1fr); }
  
  /* 桌面端布局 */
  .desktop-row {
    flex-direction: row;
    align-items: center;
  }
}

/* ===== 响应式断点媒体查询 ===== */
/* 超小屏幕 */
@media screen and (max-width: 479px) {
  .xs-hide { display: none !important; }
  .xs-show { display: block !important; }
  
  /* 超小屏幕优化 */
  .container,
  .container-fluid {
    padding-left: var(--spacing-sm);
    padding-right: var(--spacing-sm);
  }
  
  .grid-responsive {
    gap: var(--spacing-sm);
  }
  
  /* 字体缩放 */
  html {
    font-size: 14px;
  }
}

/* 小屏幕 */
@media screen and (min-width: 480px) and (max-width: 767px) {
  .sm-hide { display: none !important; }
  .sm-show { display: block !important; }
  
  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4,
  .grid-cols-5,
  .grid-cols-6 {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 中等屏幕 */
@media screen and (min-width: 768px) and (max-width: 1023px) {
  .md-hide { display: none !important; }
  .md-show { display: block !important; }
  
  .grid-cols-4,
  .grid-cols-5,
  .grid-cols-6 {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* 大屏幕 */
@media screen and (min-width: 1024px) and (max-width: 1199px) {
  .lg-hide { display: none !important; }
  .lg-show { display: block !important; }
}

/* 超大屏幕 */
@media screen and (min-width: 1200px) {
  .xl-hide { display: none !important; }
  .xl-show { display: block !important; }
}

/* ===== 高分辨率屏幕优化 ===== */
@media screen and (-webkit-min-device-pixel-ratio: 2),
       screen and (min-resolution: 192dpi),
       screen and (min-resolution: 2dppx) {
  /* 高分辨率屏幕优化 */
  .retina-border {
    border-width: 0.5px;
  }
  
  .retina-text {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* ===== 可访问性和用户偏好 ===== */
/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 高对比度偏好 */
@media (prefers-contrast: high) {
  :root {
    --border-primary: #000000;
    --text-primary: #000000;
    --bg-card: #ffffff;
  }
}

/* 深色模式偏好 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: rgba(30, 30, 30, 0.95);
    --bg-secondary: rgba(40, 40, 40, 0.8);
    --bg-card: #2a2a2a;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #999999;
    --border-primary: #404040;
    --border-secondary: rgba(255, 255, 255, 0.1);
  }
}

/* ===== 打印样式 ===== */
@media print {
  /* 隐藏不必要的元素 */
  .no-print,
  .print-hide,
  nav,
  .navigation,
  .sidebar,
  .floating-action,
  .back-to-top {
    display: none !important;
  }
  
  /* 打印优化 */
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  body {
    font-size: 12pt;
    line-height: 1.5;
  }
  
  h1, h2, h3, h4, h5, h6 {
    page-break-after: avoid;
  }
  
  p, blockquote {
    orphans: 3;
    widows: 3;
  }
  
  blockquote, pre {
    page-break-inside: avoid;
  }
  
  img {
    max-width: 100% !important;
    page-break-inside: avoid;
  }
  
  .page-break {
    page-break-before: always;
  }
}

/* ===== 实用工具类 ===== */
/* 响应式显示控制 */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.d-grid { display: grid !important; }

/* 响应式文本对齐 */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

/* 响应式浮动 */
.float-left { float: left !important; }
.float-right { float: right !important; }
.float-none { float: none !important; }

/* 响应式定位 */
.position-static { position: static !important; }
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

/* 响应式宽度 */
.w-25 { width: 25% !important; }
.w-50 { width: 50% !important; }
.w-75 { width: 75% !important; }
.w-100 { width: 100% !important; }
.w-auto { width: auto !important; }

/* 响应式高度 */
.h-25 { height: 25% !important; }
.h-50 { height: 50% !important; }
.h-75 { height: 75% !important; }
.h-100 { height: 100% !important; }
.h-auto { height: auto !important; }

/* 视口单位 */
.vw-100 { width: 100vw !important; }
.vh-100 { height: 100vh !important; }
.min-vw-100 { min-width: 100vw !important; }
.min-vh-100 { min-height: 100vh !important; }

/* Flexbox 工具 */
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.justify-start { justify-content: flex-start !important; }
.justify-end { justify-content: flex-end !important; }
.justify-center { justify-content: center !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }
.justify-evenly { justify-content: space-evenly !important; }
.align-start { align-items: flex-start !important; }
.align-end { align-items: flex-end !important; }
.align-center { align-items: center !important; }
.align-baseline { align-items: baseline !important; }
.align-stretch { align-items: stretch !important; }

/* 响应式边距和内边距的具体方向 */
.mt-responsive { margin-top: var(--spacing-responsive-md) !important; }
.mb-responsive { margin-bottom: var(--spacing-responsive-md) !important; }
.ml-responsive { margin-left: var(--spacing-responsive-md) !important; }
.mr-responsive { margin-right: var(--spacing-responsive-md) !important; }
.mx-responsive { 
  margin-left: var(--spacing-responsive-md) !important; 
  margin-right: var(--spacing-responsive-md) !important; 
}
.my-responsive { 
  margin-top: var(--spacing-responsive-md) !important; 
  margin-bottom: var(--spacing-responsive-md) !important; 
}

.pt-responsive { padding-top: var(--spacing-responsive-md) !important; }
.pb-responsive { padding-bottom: var(--spacing-responsive-md) !important; }
.pl-responsive { padding-left: var(--spacing-responsive-md) !important; }
.pr-responsive { padding-right: var(--spacing-responsive-md) !important; }
.px-responsive { 
  padding-left: var(--spacing-responsive-md) !important; 
  padding-right: var(--spacing-responsive-md) !important; 
}
.py-responsive { 
  padding-top: var(--spacing-responsive-md) !important; 
  padding-bottom: var(--spacing-responsive-md) !important; 
}