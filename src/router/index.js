import { createWebHistory, createRouter } from 'vue-router'

export const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue'),
  },
  {
    path: '/little',
    name: 'Little',
    component: () => import('../views/Little.vue'),
  },
  {
    path: '/leaving',
    name: 'Leaving',
    component: () => import('../views/Leaving.vue'),
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('../views/About.vue'),
  },
  {
    path: '/love-img',
    name: 'LoveImg',
    component: () => import('../views/LoveImg.vue'),
  },
  {
    path: '/love-list',
    name: 'LoveList',
    component: () => import('../views/LoveList.vue'),
  },
  {
    path: '/article/:id',
    name: 'ArticleDetail',
    component: () => import('../views/ArticleDetail.vue'),
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes: routes,
})

export default router
