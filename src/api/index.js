import axios from 'axios'

// 创建 axios 实例
const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 简单的错误处理
api.interceptors.response.use(
  response => response.data,
  error => {
    console.error('API Error:', error)
    return Promise.reject(error)
  }
)

// 网站配置 API
export const getSiteConfig = () => api.get('/site/config')

// 文章 API
export const getArticles = (params = {}) => api.get('/articles', { params })
export const getArticleById = (id) => api.get(`/articles/${id}`)
export const getRelatedArticles = (id, limit = 5) => api.get(`/articles/${id}/related`, { params: { limit } })

// 留言 API
export const getMessages = (params = {}) => api.get('/messages', { params })
export const submitMessage = (data) => api.post('/messages', data)


// 恋爱相册 API
export const getLoveImages = (params = {}) => api.get('/love-images', { params })
export const uploadLoveImage = (file) => {
  const formData = new FormData()
  formData.append('image', file)
  return api.post('/love-images/upload', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

// 恋爱清单 API
export const getLoveList = () => api.get('/love-list')

// 关于页面 API
export const getAboutInfo = () => api.get('/about')

// 健康检查 API
export const healthCheck = () => api.get('/health')

export default api
