import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getSiteConfig } from '../api'
import dayjs from 'dayjs'

export const useSiteStore = defineStore('site', () => {
  const config = ref({
    title: '毛双欢和张家伟的小窝',
    logo: '一二&布布',
    writing: '喜欢花 喜欢浪漫 喜欢你~',
    boy: 'zjw',
    girl: 'msh', 
    boyQQ: '2087368143',
    girlQQ: '1553517090',
    startTime: '2023-12-29 00:00:00',
    animation: '1',
    copyright: '',
    icp: '',
  })

  const loading = ref(false)

  // 计算恋爱天数
  const loveTime = computed(() => {
    const now = dayjs()
    const start = dayjs(config.value.startTime)
    const diff = now.diff(start, 'millisecond')
    
    const days = Math.floor(diff / (24 * 60 * 60 * 1000))
    const hours = Math.floor((diff % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000))
    const minutes = Math.floor((diff % (60 * 60 * 1000)) / (60 * 1000))
    const seconds = Math.floor((diff % (60 * 1000)) / 1000)
    
    return {
      days,
      hours,
      minutes,
      seconds
    }
  })

  // 获取网站配置
  const fetchSiteConfig = async () => {
    try {
      loading.value = true
      const response = await getSiteConfig()
      config.value = { ...config.value, ...response.data }
    } catch (error) {
      console.error('获取网站配置失败:', error)
    } finally {
      loading.value = false
    }
  }

  return {
    config,
    loading,
    loveTime,
    fetchSiteConfig
  }
})
