import { ref, computed, onMounted, onUnmounted } from 'vue'

/**
 * 简单的响应式设计 Hook
 */
export function useResponsive() {
  const windowWidth = ref(window.innerWidth)
  
  const updateWindowSize = () => {
    windowWidth.value = window.innerWidth
  }
  
  const isMobile = computed(() => windowWidth.value < 768)
  const isTablet = computed(() => windowWidth.value >= 768 && windowWidth.value < 1024)
  const isDesktop = computed(() => windowWidth.value >= 1024)
  
  onMounted(() => {
    window.addEventListener('resize', updateWindowSize)
  })
  
  onUnmounted(() => {
    window.removeEventListener('resize', updateWindowSize)
  })
  
  return {
    windowWidth,
    isMobile,
    isTablet,
    isDesktop
  }
}
